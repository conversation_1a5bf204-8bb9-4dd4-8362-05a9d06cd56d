{% extends "index.html" %}

{% block content %}
<!-- Test Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="dashboard-title mb-0">
                <i class="bi bi-tools me-2"></i>Database Test
            </h1>
            <div class="badge bg-warning fs-6 px-3 py-2">
                <i class="bi bi-exclamation-triangle me-1"></i>
                Testing Mode
            </div>
        </div>
    </div>
</div>

<!-- Test Controls -->
<div class="row mb-4 g-3">
    <div class="col-lg-6">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <h5 class="mb-0 text-white">
                    <i class="bi bi-database-add me-2"></i>Add Test Data
                </h5>
            </div>
            <div class="card-body p-4">
                <p class="text-muted mb-3">
                    Click the button below to add sample machine readings to your database. 
                    This will create 5 test events with different durations.
                </p>
                <button id="addTestDataBtn" class="btn btn-success btn-lg w-100">
                    <i class="bi bi-plus-circle me-2"></i>Add Test Data
                </button>
                <div id="testResult" class="mt-3"></div>
            </div>
        </div>
    </div>
    
    <div class="col-lg-6">
        <div class="card h-100 border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
                <h5 class="mb-0 text-white">
                    <i class="bi bi-graph-up me-2"></i>View Results
                </h5>
            </div>
            <div class="card-body p-4">
                <p class="text-muted mb-3">
                    After adding test data, check these pages to verify the database integration:
                </p>
                <div class="d-grid gap-2">
                    <a href="/dashboard" class="btn btn-outline-primary">
                        <i class="bi bi-speedometer2 me-2"></i>View Dashboard
                    </a>
                    <a href="/events" class="btn btn-outline-info">
                        <i class="bi bi-list-ul me-2"></i>View Events
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Manual Test Form -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #fd7e14 0%, #e83e8c 100%);">
                <h5 class="mb-0 text-white">
                    <i class="bi bi-pencil-square me-2"></i>Manual Test Entry
                </h5>
            </div>
            <div class="card-body p-4">
                <form id="manualTestForm">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="startTime" class="form-label">Start Time</label>
                            <input type="datetime-local" class="form-control" id="startTime" required>
                        </div>
                        <div class="col-md-4">
                            <label for="endTime" class="form-label">End Time</label>
                            <input type="datetime-local" class="form-control" id="endTime" required>
                        </div>
                        <div class="col-md-4">
                            <label for="duration" class="form-label">Duration (seconds)</label>
                            <input type="number" class="form-control" id="duration" min="1" required>
                        </div>
                    </div>
                    <div class="mt-3">
                        <button type="submit" class="btn btn-warning">
                            <i class="bi bi-send me-2"></i>Send Manual Entry
                        </button>
                        <button type="button" id="fillCurrentBtn" class="btn btn-outline-secondary ms-2">
                            <i class="bi bi-clock me-2"></i>Fill Current Time
                        </button>
                    </div>
                    <div id="manualResult" class="mt-3"></div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- API Test Results -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-header" style="background: linear-gradient(135deg, #6c757d 0%, #495057 100%);">
                <h5 class="mb-0 text-white">
                    <i class="bi bi-code-slash me-2"></i>API Test Results
                </h5>
            </div>
            <div class="card-body p-4">
                <button id="testApiBtn" class="btn btn-secondary mb-3">
                    <i class="bi bi-arrow-clockwise me-2"></i>Test All API Endpoints
                </button>
                <div id="apiResults">
                    <p class="text-muted">Click the button above to test all API endpoints</p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add test data functionality
document.getElementById('addTestDataBtn').addEventListener('click', async function() {
    const btn = this;
    const resultDiv = document.getElementById('testResult');
    
    btn.disabled = true;
    btn.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>Adding Test Data...';
    
    try {
        const response = await fetch('/api/test-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (response.ok) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>
                    ${data.message}
                </div>
            `;
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Error: ${data.error}
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Network error: ${error.message}
            </div>
        `;
    }
    
    btn.disabled = false;
    btn.innerHTML = '<i class="bi bi-plus-circle me-2"></i>Add Test Data';
});

// Fill current time functionality
document.getElementById('fillCurrentBtn').addEventListener('click', function() {
    const now = new Date();
    const fiveMinutesAgo = new Date(now.getTime() - 5 * 60000);
    
    document.getElementById('startTime').value = fiveMinutesAgo.toISOString().slice(0, 16);
    document.getElementById('endTime').value = now.toISOString().slice(0, 16);
    document.getElementById('duration').value = 300; // 5 minutes
    
    // Show feedback
    const manualResult = document.getElementById('manualResult');
    manualResult.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i>
            Form filled with current time! Start: ${fiveMinutesAgo.toLocaleString()}, End: ${now.toLocaleString()}, Duration: 5 minutes
        </div>
    `;
    
    // Change button text temporarily
    const btn = this;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="bi bi-check me-2"></i>Filled!';
    btn.classList.add('btn-success');
    btn.classList.remove('btn-outline-secondary');
    
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.classList.remove('btn-success');
        btn.classList.add('btn-outline-secondary');
    }, 2000);
});

// Manual test form
document.getElementById('manualTestForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const resultDiv = document.getElementById('manualResult');
    const startTime = document.getElementById('startTime').value;
    const endTime = document.getElementById('endTime').value;
    const duration = parseInt(document.getElementById('duration').value);
    
    // Validate inputs
    if (!startTime || !endTime || !duration) {
        resultDiv.innerHTML = `
            <div class="alert alert-warning">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Please fill in all fields
            </div>
        `;
        return;
    }
    
    const data = {
        start_time: new Date(startTime).toISOString(),
        end_time: new Date(endTime).toISOString(),
        duration: duration,
        api_key: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MDI0NjYsImV4cCI6MjA2NjE3ODQ2Nn0.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE'
    };
    
    // Show loading state
    resultDiv.innerHTML = `
        <div class="alert alert-info">
            <i class="bi bi-arrow-clockwise spin me-2"></i>
            Sending manual entry...
        </div>
    `;

    try {
        const response = await fetch('/api/machine-data', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (response.ok) {
            resultDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="bi bi-check-circle me-2"></i>
                    Manual entry sent successfully!
                </div>
            `;
            this.reset();
        } else {
            resultDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    <strong>Error:</strong> ${result.error || 'Unknown error'}<br>
                    ${result.details ? `<small>Details: ${result.details}</small>` : ''}
                    <br><small>Status: ${response.status}</small>
                </div>
            `;
        }
    } catch (error) {
        resultDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="bi bi-exclamation-triangle me-2"></i>
                Network error: ${error.message}
            </div>
        `;
    }
});

// Test API endpoints
document.getElementById('testApiBtn').addEventListener('click', async function() {
    const btn = this;
    const resultsDiv = document.getElementById('apiResults');
    
    btn.disabled = true;
    btn.innerHTML = '<i class="bi bi-arrow-clockwise spin me-2"></i>Testing APIs...';
    
    const endpoints = [
        { url: '/api/machine-data', method: 'GET', name: 'Get Machine Data' },
        { url: '/api/summary', method: 'GET', name: 'Get Summary' },
        { url: '/api/charts/daily-runtime', method: 'GET', name: 'Daily Runtime Chart' },
        { url: '/api/charts/hourly-distribution', method: 'GET', name: 'Hourly Distribution Chart' },
        { url: '/api/charts/duration-distribution', method: 'GET', name: 'Duration Distribution Chart' }
    ];
    
    let results = '<div class="row g-2">';
    
    for (const endpoint of endpoints) {
        try {
            const response = await fetch(endpoint.url);
            const status = response.ok ? 'success' : 'danger';
            const icon = response.ok ? 'check-circle' : 'x-circle';
            
            results += `
                <div class="col-md-6">
                    <div class="alert alert-${status} mb-2 py-2">
                        <i class="bi bi-${icon} me-2"></i>
                        <strong>${endpoint.name}:</strong> ${response.status}
                    </div>
                </div>
            `;
        } catch (error) {
            results += `
                <div class="col-md-6">
                    <div class="alert alert-danger mb-2 py-2">
                        <i class="bi bi-x-circle me-2"></i>
                        <strong>${endpoint.name}:</strong> Error
                    </div>
                </div>
            `;
        }
    }
    
    results += '</div>';
    resultsDiv.innerHTML = results;
    
    btn.disabled = false;
    btn.innerHTML = '<i class="bi bi-arrow-clockwise me-2"></i>Test All API Endpoints';
});

// Add spinning animation for loading states
const style = document.createElement('style');
style.textContent = `
    .spin {
        animation: spin 1s linear infinite;
    }
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}