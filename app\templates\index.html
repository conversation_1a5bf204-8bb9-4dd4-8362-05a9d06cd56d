<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Machine Monitoring System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <!-- DataTables CSS -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='favicon.svg') }}">
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="shortcut icon" href="/favicon.ico">
</head>
<body class="d-flex flex-column min-vh-100">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <img src="{{ url_for('static', filename='images/logo.jpeg') }}"
                     alt="Ankur Technologies"
                     class="navbar-logo"
                     style="height: 45px;
                            max-width: 180px;
                            object-fit: contain;
                            margin-right: 15px;
                            background: rgba(255,255,255,0.1);
                            padding: 5px;
                            border-radius: 8px;
                            transition: all 0.3s ease;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                <span class="fw-bold logo-fallback" style="display: none; color: white; font-size: 1.2rem;">AT</span>
                <span class="fw-bold" style="font-size: 1.1rem; letter-spacing: 0.5px; color: white;">Machine Monitoring System</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="/events">
                            <i class="bi bi-list-ul me-1"></i>Events
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link d-flex align-items-center" href="/test">
                            <i class="bi bi-tools me-1"></i>Test
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav ms-auto">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-2"></i>
                            <span id="username-display">{{ session.username if session.username else 'User' }}</span>
                            <span class="badge bg-light text-dark ms-2" id="machine-badge">
                                {{ session.machine_name if session.machine_name else 'Machine' }}
                            </span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">
                                <i class="bi bi-cpu me-2"></i>{{ session.machine_name if session.machine_name else 'Machine Info' }}
                            </h6></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="/profile">
                                <i class="bi bi-person me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="/settings">
                                <i class="bi bi-gear me-2"></i>Settings
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="/logout">
                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="container my-4 flex-grow-1">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    <footer class="bg-light py-4 mt-auto border-top">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start mb-3 mb-md-0">
                    <p class="mb-0">
                        <i class="bi bi-c-circle me-1"></i>Ankur Technologies {{ now.year }}
                    </p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <small class="text-muted">
                        <span id="status-indicator" class="d-inline-block me-2">
                            <span class="d-inline-block rounded-circle bg-success p-1"></span>
                            System Operational
                        </span>
                        <span id="current-time">
                            {{ now.strftime('%Y-%m-%d %H:%M:%S IST') }}
                        </span>
                    </small>
                </div>
            </div>
        </div>
    </footer>

    <!-- JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script src="{{ url_for('static', filename='js/test-suite.js') }}"></script>
    
    <!-- Page-specific Scripts -->
    {% block scripts %}{% endblock %}
    
    <!-- Real-time Clock -->
    <script>
        // Update clock in footer with IST time
        function updateClock() {
            try {
                // Get current time in IST using browser's timezone API
                const now = new Date();

                // Method 1: Use toLocaleString with Asia/Kolkata timezone
                const istTimeString = now.toLocaleString('en-CA', {
                    timeZone: 'Asia/Kolkata',
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });

                // Format: YYYY-MM-DD HH:MM:SS IST
                const formattedTime = istTimeString.replace(',', '') + ' IST';

                // Update the time display
                const timeElement = document.getElementById('current-time');
                if (timeElement) {
                    timeElement.textContent = formattedTime;
                }
            } catch (error) {
                // Fallback method if timezone API fails
                console.log('Using fallback time method');
                const now = new Date();
                const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
                const istTime = new Date(now.getTime() + istOffset);

                const year = istTime.getUTCFullYear();
                const month = String(istTime.getUTCMonth() + 1).padStart(2, '0');
                const day = String(istTime.getUTCDate()).padStart(2, '0');
                const hours = String(istTime.getUTCHours()).padStart(2, '0');
                const minutes = String(istTime.getUTCMinutes()).padStart(2, '0');
                const seconds = String(istTime.getUTCSeconds()).padStart(2, '0');

                const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds} IST`;

                const timeElement = document.getElementById('current-time');
                if (timeElement) {
                    timeElement.textContent = formattedTime;
                }
            }
        }
        
        // Update every second
        setInterval(updateClock, 1000);
        updateClock();
        
        // System status indicator
        function checkSystemStatus() {
            fetch('/api/summary')
                .then(response => response.json())
                .then(data => {
                    const indicator = document.getElementById('status-indicator');
                    if (data.last_event) {
                        const lastEventEnd = new Date(data.last_event.end_time);
                        const minutesAgo = Math.floor((new Date() - lastEventEnd) / 60000);
                        
                        if (minutesAgo < 5) {
                            indicator.innerHTML = `
                                <span class="d-inline-block rounded-circle bg-success p-1"></span>
                                System Operational
                            `;
                        } else {
                            indicator.innerHTML = `
                                <span class="d-inline-block rounded-circle bg-warning p-1"></span>
                                No recent activity
                            `;
                        }
                    } else {
                        indicator.innerHTML = `
                            <span class="d-inline-block rounded-circle bg-secondary p-1"></span>
                            No data available
                        `;
                    }
                })
                .catch(error => {
                    document.getElementById('status-indicator').innerHTML = `
                        <span class="d-inline-block rounded-circle bg-danger p-1"></span>
                        Connection Error
                    `;
                });
        }
        
        // Check status every 30 seconds
        setInterval(checkSystemStatus, 30000);
        checkSystemStatus();
    </script>
</body>
</html>