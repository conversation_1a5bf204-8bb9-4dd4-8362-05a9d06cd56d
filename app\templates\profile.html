<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - Machine Monitoring System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <img src="{{ url_for('static', filename='images/logo.jpeg') }}"
                     alt="Ankur Technologies"
                     class="navbar-logo"
                     style="height: 45px;
                            max-width: 180px;
                            object-fit: contain;
                            margin-right: 15px;
                            background: rgba(255,255,255,0.1);
                            padding: 5px;
                            border-radius: 8px;
                            transition: all 0.3s ease;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                <span class="fw-bold logo-fallback" style="display: none; color: white; font-size: 1.2rem;">AT</span>
                <span class="fw-bold" style="font-size: 1.1rem; letter-spacing: 0.5px; color: white;">Machine Monitoring System</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/events">
                            <i class="bi bi-list-ul me-1"></i>Events
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/profile">
                            <i class="bi bi-person-circle me-1"></i>Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-danger" href="/logout">
                            <i class="bi bi-box-arrow-right me-1"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-person-circle me-2"></i>
                            User Profile
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted">Account Information</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Username:</strong></td>
                                        <td>{{ session.username }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Role:</strong></td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if session.role == 'admin' else 'primary' }}">
                                                {{ session.role.title() }}
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Login Time:</strong></td>
                                        <td>{{ now.strftime('%Y-%m-%d %H:%M:%S IST') }}</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-muted">Machine Access</h6>
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>Machine Name:</strong></td>
                                        <td>{{ session.machine_name }}</td>
                                    </tr>
                                    <tr>
                                        <td><strong>Machine IDs:</strong></td>
                                        <td>
                                            {% for machine_id in session.machine_ids %}
                                                <span class="badge bg-secondary me-1">{{ machine_id }}</span>
                                            {% endfor %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>Access Level:</strong></td>
                                        <td>
                                            {% if session.role == 'admin' %}
                                                <span class="text-success">
                                                    <i class="bi bi-shield-check me-1"></i>
                                                    Full Access (All Machines)
                                                </span>
                                            {% else %}
                                                <span class="text-primary">
                                                    <i class="bi bi-shield me-1"></i>
                                                    Limited Access (Assigned Machines Only)
                                                </span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted">Available Actions</h6>
                                <div class="d-flex gap-2 flex-wrap">
                                    <a href="/dashboard" class="btn btn-primary">
                                        <i class="bi bi-speedometer2 me-1"></i>
                                        View Dashboard
                                    </a>
                                    <a href="/events" class="btn btn-outline-primary">
                                        <i class="bi bi-list-ul me-1"></i>
                                        View Events
                                    </a>
                                    <a href="/api/export/events-csv" class="btn btn-outline-success">
                                        <i class="bi bi-download me-1"></i>
                                        Export Data
                                    </a>
                                    <a href="/logout" class="btn btn-outline-danger">
                                        <i class="bi bi-box-arrow-right me-1"></i>
                                        Logout
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            System Information
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <strong>Data Filtering:</strong><br>
                                    You can only see data from machines you have access to.
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <strong>Security:</strong><br>
                                    Your session is secure and will expire when you logout.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
