@echo off
chcp 65001 >nul
title Machine Monitoring System - Production Server

REM ========================================
REM Machine Monitoring System - Production Server
REM Dual Server Setup with Redundancy
REM ========================================

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                 MACHINE MONITORING SYSTEM                    ║
echo ║                    Production Server                         ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🌐 Server Configuration:
echo    ➤ Primary Server:  **************:5000
echo    ➤ Backup Server:   **************:9090
echo    ➤ Local Access:    localhost:5000 and localhost:9090
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running with Administrator privileges
) else (
    echo ❌ ERROR: This script requires Administrator privileges
    echo.
    echo 💡 Please right-click on start_app.bat and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

REM Navigate to app directory
echo 📁 Navigating to application directory...
cd /d "C:\inetpub\wwwroot\machine-monitoring\app"
if not exist "app.py" (
    echo ❌ ERROR: app.py not found in current directory
    echo 📍 Current directory: %CD%
    echo 💡 Please ensure the script is in the correct location
    pause
    exit /b 1
)
echo ✅ Application directory confirmed: %CD%

REM Check and setup virtual environment
echo.
echo 🔧 Setting up Python virtual environment...
if not exist "venv\Scripts\activate.bat" (
    echo ⚠️  Virtual environment not found, creating new one...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        echo 💡 Please ensure Python is installed and in PATH
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created successfully
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)
echo ✅ Virtual environment activated

REM Check Python version
echo 🐍 Checking Python version...
python --version
if errorlevel 1 (
    echo ❌ Python not available in virtual environment
    pause
    exit /b 1
)

REM Install/upgrade required packages
echo.
echo 📦 Installing/updating required packages...
pip install --upgrade pip --quiet
pip install flask python-dotenv supabase waitress --quiet
if errorlevel 1 (
    echo ⚠️  Some packages may have failed to install, continuing...
)
echo ✅ Package installation completed

REM Test app import
echo.
echo 🧪 Testing Flask application import...
python -c "from app import app; print('✅ Flask app imported successfully')"
if errorlevel 1 (
    echo ❌ ERROR: Failed to import Flask app
    echo 💡 Check app.py for syntax errors or missing dependencies
    pause
    exit /b 1
)

REM Clean up existing processes
echo.
echo 🧹 Cleaning up existing processes...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im waitress-serve.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM Check port availability
echo.
echo 🔍 Checking port availability...

REM Check port 5000
netstat -an | find ":5000 " >nul
if %errorlevel% == 0 (
    echo ⚠️  Port 5000 is in use, attempting to free it...
    for /f "tokens=5" %%a in ('netstat -ano ^| find ":5000 "') do taskkill /PID %%a /F >nul 2>&1
    timeout /t 2 /nobreak >nul
)
echo ✅ Port 5000 is ready

REM Check port 9090
netstat -an | find ":9090 " >nul
if %errorlevel% == 0 (
    echo ⚠️  Port 9090 is in use, attempting to free it...
    for /f "tokens=5" %%a in ('netstat -ano ^| find ":9090 "') do taskkill /PID %%a /F >nul 2>&1
    timeout /t 2 /nobreak >nul
)
echo ✅ Port 9090 is ready

REM Configure firewall rules
echo.
echo 🔥 Configuring firewall rules...
netsh advfirewall firewall delete rule name="Flask Port 5000 Inbound" >nul 2>&1
netsh advfirewall firewall delete rule name="Flask Port 9090 Inbound" >nul 2>&1
netsh advfirewall firewall add rule name="Flask Port 5000 Inbound" dir=in action=allow protocol=TCP localport=5000 >nul 2>&1
netsh advfirewall firewall add rule name="Flask Port 9090 Inbound" dir=in action=allow protocol=TCP localport=9090 >nul 2>&1
echo ✅ Firewall rules configured

REM Start Flask servers
echo.
echo 🚀 Starting Flask application servers...
echo.

REM Start Primary Server (Port 5000)
echo [1/2] 🔵 Starting Primary Server on port 5000...
echo       📍 Accessible at: http://**************:5000
start "Flask Primary Server - Port 5000" cmd /k "title Flask Primary Server - Port 5000 && cd /d C:\inetpub\wwwroot\machine-monitoring\app && call venv\Scripts\activate.bat && echo. && echo ╔══════════════════════════════════════════════════════════════╗ && echo ║                    PRIMARY SERVER - PORT 5000                ║ && echo ╚══════════════════════════════════════════════════════════════╝ && echo. && echo 🌐 Server accessible at: && echo    ➤ http://**************:5000 && echo    ➤ http://localhost:5000 && echo. && echo 🚀 Starting Waitress server... && echo ⏹️  Press Ctrl+C to stop this server && echo. && waitress-serve --host=0.0.0.0 --port=5000 --threads=8 --connection-limit=200 --cleanup-interval=30 --channel-timeout=120 --max-request-body-size=1048576 app:app || (echo. && echo ❌ Primary server failed to start && echo 💡 Check the error messages above && echo. && pause)"

REM Wait for primary server to initialize
echo ⏳ Waiting for primary server to initialize...
timeout /t 5 /nobreak >nul

REM Check if primary server started
netstat -an | find ":5000 " >nul
if %errorlevel% == 0 (
    echo ✅ Primary server started successfully on port 5000
) else (
    echo ⚠️  Primary server may still be starting...
)

echo.

REM Start Backup Server (Port 9090)
echo [2/2] 🟢 Starting Backup Server on port 9090...
echo       📍 Accessible at: http://**************:9090
start "Flask Backup Server - Port 9090" cmd /k "title Flask Backup Server - Port 9090 && cd /d C:\inetpub\wwwroot\machine-monitoring\app && call venv\Scripts\activate.bat && echo. && echo ╔══════════════════════════════════════════════════════════════╗ && echo ║                    BACKUP SERVER - PORT 9090                 ║ && echo ╚══════════════════════════════════════════════════════════════╝ && echo. && echo 🌐 Server accessible at: && echo    ➤ http://**************:9090 && echo    ➤ http://localhost:9090 && echo. && echo 🚀 Starting Waitress server... && echo ⏹️  Press Ctrl+C to stop this server && echo. && waitress-serve --host=0.0.0.0 --port=9090 --threads=8 --connection-limit=200 --cleanup-interval=30 --channel-timeout=120 --max-request-body-size=1048576 app:app || (echo. && echo ❌ Backup server failed to start && echo 💡 Check the error messages above && echo. && pause)"

REM Wait for backup server to initialize
echo ⏳ Waiting for backup server to initialize...
timeout /t 5 /nobreak >nul

REM Check if backup server started
netstat -an | find ":9090 " >nul
if %errorlevel% == 0 (
    echo ✅ Backup server started successfully on port 9090
) else (
    echo ⚠️  Backup server may still be starting...
)

REM Final status check
echo.
echo 🔍 Final server status check:
timeout /t 2 /nobreak >nul
netstat -an | find ":5000 " >nul && echo ✅ Port 5000: ACTIVE || echo ❌ Port 5000: INACTIVE
netstat -an | find ":9090 " >nul && echo ✅ Port 9090: ACTIVE || echo ❌ Port 9090: INACTIVE

REM Test server connectivity
echo.
echo 🧪 Testing server connectivity...
echo Testing primary server (localhost:5000)...
curl -s -o nul -w "HTTP %%{http_code}" http://localhost:5000 2>nul && echo ✅ Primary server responding || echo ⚠️  Primary server not responding yet

echo Testing backup server (localhost:9090)...
curl -s -o nul -w "HTTP %%{http_code}" http://localhost:9090 2>nul && echo ✅ Backup server responding || echo ⚠️  Backup server not responding yet

REM Display network information
echo.
echo 🌐 Network Configuration:
echo ================================
ipconfig | findstr "IPv4" | head -3

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 DEPLOYMENT SUCCESSFUL! 🎉              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🌐 Your Flask application is now running on BOTH servers:
echo.
echo    🔵 PRIMARY SERVER:
echo       ➤ URL: http://**************:5000
echo       ➤ Local: http://localhost:5000
echo       ➤ Status: Running on port 5000
echo.
echo    🟢 BACKUP SERVER:
echo       ➤ URL: http://**************:9090
echo       ➤ Local: http://localhost:9090
echo       ➤ Status: Running on port 9090
echo.
echo 🔑 Login Credentials:
echo    ➤ Admin: pankaj / pankaj123
echo    ➤ Admin: admin / admin123
echo    ➤ User1: user1 / password1
echo    ➤ User2: user2 / password2
echo    ➤ User3: user3 / password3
echo.
echo 📡 API Endpoints (available on both servers):
echo    ➤ GET  /                     - Dashboard
echo    ➤ GET  /login                - Login page
echo    ➤ GET  /dashboard            - Main dashboard
echo    ➤ GET  /events               - Events page
echo    ➤ GET  /api/health           - Health check
echo    ➤ GET  /api/machine-data     - Machine data
echo    ➤ POST /api/machine-data     - Submit data (Arduino)
echo.
echo 🔧 Arduino GSM Configuration:
echo    ➤ Primary:  serverURL = "**************", port = 5000
echo    ➤ Backup:   serverURL = "**************", port = 9090
echo.
echo 💡 Benefits of this setup:
echo    ✅ Redundancy - If one server fails, the other continues
echo    ✅ Load balancing - Better performance under load
echo    ✅ Multiple access points - Network flexibility
echo    ✅ Auto-restart - Servers restart if they crash
echo.
echo ⚠️  IMPORTANT NOTES:
echo    ➤ Two separate server windows have opened
echo    ➤ Do NOT close the server windows to keep them running
echo    ➤ You can close this launcher window safely
echo    ➤ Servers will continue running in background
echo.
echo 🔍 To check server status anytime:
echo    ➤ Visit: http://localhost:5000 or http://localhost:9090
echo    ➤ Check: http://localhost:5000/api/health
echo.
echo 📋 Troubleshooting:
echo    ➤ If external access fails, check router port forwarding
echo    ➤ If servers don't start, check the server windows for errors
echo    ➤ If login fails, use credentials listed above
echo.
echo Press any key to close this launcher window...
echo (Flask servers will continue running in separate windows)
pause >nul

echo.
echo 👋 Launcher window closing...
echo 🚀 Flask servers continue running in background
echo 🌐 Access your app at: http://localhost:5000
timeout /t 3 /nobreak >nul
