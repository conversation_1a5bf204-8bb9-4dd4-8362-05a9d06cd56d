import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os

class AppService(win32serviceutil.ServiceFramework):
    _svc_name_ = "MachineMonitoring"
    _svc_display_name_ = "Machine Monitoring Web Service"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        socket.setdefaulttimeout(60)
        
    def SvcStop(self):
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        
    def SvcDoRun(self):
        servicemanager.LogMsg(servicemanager.EVENTLOG_INFORMATION_TYPE,
                              servicemanager.PYS_SERVICE_STARTED,
                              (self._svc_name_, ''))
        self.main()
        
    def main(self):
        os.chdir("C:\\machine_monitoring")
        os.system("venv\\Scripts\\python.exe -m waitress --listen=0.0.0.0:5000 wsgi:app")