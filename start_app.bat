@echo off
chcp 65001 >nul
title Machine Monitoring System - Production Server

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running with Administrator privileges
) else (
    echo ❌ ERROR: This script requires Administrator privileges
    echo.
    echo Please right-click on start_app.bat and select "Run as administrator"
    echo.
    pause
    exit /b 1
)

echo ========================================
echo Machine Monitoring System - Production Server
echo ========================================
echo Server: **************
echo Static IP: **************
echo ========================================
echo.

REM Navigate to app directory with error checking
echo 📁 Navigating to application directory...
cd /d "C:\inetpub\wwwroot\machine-monitoring\app"
if not exist "app.py" (
    echo ❌ ERROR: app.py not found in current directory
    echo 📍 Current directory: %CD%
    echo 💡 Please ensure the script is in the correct location
    pause
    exit /b 1
)
echo ✅ Application directory confirmed: %CD%

REM Check and activate virtual environment
echo 🔄 Activating virtual environment...
if not exist "venv\Scripts\activate.bat" (
    echo ⚠️  Virtual environment not found, creating new one...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        echo 💡 Please ensure Python is installed and in PATH
        pause
        exit /b 1
    )
    echo ✅ Virtual environment created successfully
)

call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)
echo ✅ Virtual environment activated

REM Install/upgrade required packages
echo 📦 Installing/updating required packages...
pip install --upgrade pip --quiet
pip install flask python-dotenv supabase waitress --quiet
if errorlevel 1 (
    echo ⚠️  Some packages may have failed to install, continuing...
)
echo ✅ Package installation completed

echo 🧪 Testing app import...
python -c "from app import app; print('✅ App imported successfully')"
if errorlevel 1 (
    echo ❌ ERROR: Failed to import app
    pause
    exit /b 1
)

echo.
echo 🔍 Checking port availability...

REM Check if port 5000 is available
netstat -an | find ":5000 " >nul
if %errorlevel% == 0 (
    echo ⚠️  Port 5000 is already in use
    echo 🔄 Will try to stop existing process...
    for /f "tokens=5" %%a in ('netstat -ano ^| find ":5000 "') do taskkill /PID %%a /F >nul 2>&1
    timeout /t 2 /nobreak >nul
) else (
    echo ✅ Port 5000 is available
)

REM Check if port 9090 is available
netstat -an | find ":9090 " >nul
if %errorlevel% == 0 (
    echo ⚠️  Port 9090 is already in use
    echo 🔄 Will try to stop existing process...
    for /f "tokens=5" %%a in ('netstat -ano ^| find ":9090 "') do taskkill /PID %%a /F >nul 2>&1
    timeout /t 2 /nobreak >nul
) else (
    echo ✅ Port 9090 is available
)

echo.
echo 🚀 Starting Flask application with Waitress...
echo.
echo 🌐 Server will be accessible at:
echo    ➤ Static IP: http://**************:9090
echo    ➤ Server IP: http://**************:5000
echo    ➤ Local: http://127.0.0.1:5000
echo.
echo ⏹️  Press Ctrl+C to stop the server
echo.

REM Kill any existing Flask/Waitress processes first
echo 🧹 Cleaning up existing processes...
taskkill /f /im python.exe >nul 2>&1
taskkill /f /im waitress-serve.exe >nul 2>&1
timeout /t 2 /nobreak >nul

REM Start multiple Flask instances for redundancy
echo 🔄 Starting multiple server instances...
echo.

REM Start first instance on port 5000 (Primary Server) - Listen on all interfaces
echo [1/2] Starting Primary Server on port 5000 (accessible via **************:5000)...
echo     Binding to all interfaces (0.0.0.0) for maximum compatibility...
start "Flask Primary - Port 5000" cmd /k "title Flask Primary Server - Port 5000 && cd /d C:\inetpub\wwwroot\machine-monitoring\app && call venv\Scripts\activate.bat && echo. && echo ╔══════════════════════════════════════════════════════════════╗ && echo ║                    PRIMARY SERVER - PORT 5000                ║ && echo ╚══════════════════════════════════════════════════════════════╝ && echo. && echo 🌐 Server accessible at: && echo    ➤ http://**************:5000 && echo    ➤ http://localhost:5000 && echo. && echo 🚀 Starting Waitress server... && echo ⏹️  Press Ctrl+C to stop this server && echo. && waitress-serve --host=0.0.0.0 --port=5000 --threads=8 --connection-limit=200 --cleanup-interval=30 --channel-timeout=120 --max-request-body-size=1048576 app:app || (echo. && echo ❌ Primary server failed to start && echo 💡 Check the error messages above && echo. && pause)"

REM Wait for first instance to start
echo ⏳ Waiting for primary server to initialize...
timeout /t 6 /nobreak >nul

REM Check if first server started successfully
netstat -an | find ":5000 " >nul
if %errorlevel% == 0 (
    echo ✅ Primary server started successfully on port 5000
    echo 🧪 Testing primary server...
    curl -s -o nul -w "HTTP %%{http_code}" http://localhost:5000 2>nul && echo " - Response OK" || echo " - No response"
) else (
    echo ❌ Primary server failed to start on port 5000
)

echo.
REM Start second instance on port 9090 (Backup Server) - Listen on all interfaces
echo [2/2] Starting Backup Server on port 9090 (accessible via **************:9090)...
echo     Binding to all interfaces (0.0.0.0) for maximum compatibility...
start "Flask Backup - Port 9090" cmd /k "title Flask Backup Server - Port 9090 && cd /d C:\inetpub\wwwroot\machine-monitoring\app && call venv\Scripts\activate.bat && echo. && echo ╔══════════════════════════════════════════════════════════════╗ && echo ║                    BACKUP SERVER - PORT 9090                 ║ && echo ╚══════════════════════════════════════════════════════════════╝ && echo. && echo 🌐 Server accessible at: && echo    ➤ http://**************:9090 && echo    ➤ http://localhost:9090 && echo. && echo 🚀 Starting Waitress server... && echo ⏹️  Press Ctrl+C to stop this server && echo. && waitress-serve --host=0.0.0.0 --port=9090 --threads=8 --connection-limit=200 --cleanup-interval=30 --channel-timeout=120 --max-request-body-size=1048576 app:app || (echo. && echo ❌ Backup server failed to start && echo 💡 Check the error messages above && echo. && pause)"

echo ⏳ Waiting for backup server to initialize...
timeout /t 6 /nobreak >nul

REM Check if second server started successfully
netstat -an | find ":9090 " >nul
if %errorlevel% == 0 (
    echo ✅ Backup server started successfully on port 9090
    echo 🧪 Testing backup server...
    curl -s -o nul -w "HTTP %%{http_code}" http://localhost:9090 2>nul && echo " - Response OK" || echo " - No response"
) else (
    echo ❌ Backup server failed to start on port 9090
)

echo.
echo 🔍 Final server status check:
netstat -an | find ":5000 " >nul && echo ✅ Port 5000: ACTIVE || echo ❌ Port 5000: INACTIVE
netstat -an | find ":9090 " >nul && echo ✅ Port 9090: ACTIVE || echo ❌ Port 9090: INACTIVE

echo.
echo 🌐 Network Configuration Check:
echo ================================
echo Checking if static IPs are configured on this machine...
ipconfig | findstr "IPv4"
echo.
echo 🔍 Checking for static IP **************:
ipconfig | findstr "**************" >nul && echo ✅ Static IP ************** is configured || echo ❌ Static IP ************** NOT found
echo.
echo 🔍 Checking for server IP **************:
ipconfig | findstr "**************" >nul && echo ✅ Server IP ************** is configured || echo ❌ Server IP ************** NOT found
echo.
echo 🔥 Firewall Rules Check:
netsh advfirewall firewall show rule name="Flask Port 5000 Inbound" >nul 2>&1 && echo ✅ Port 5000 firewall rule exists || echo ❌ Port 5000 firewall rule missing
netsh advfirewall firewall show rule name="Flask Port 9090 Inbound" >nul 2>&1 && echo ✅ Port 9090 firewall rule exists || echo ❌ Port 9090 firewall rule missing

echo.
echo 🧪 Testing Local Access:
echo ========================
echo Testing localhost:5000...
curl -s -o nul -w "%%{http_code}" http://localhost:5000 2>nul && echo ✅ localhost:5000 responds || echo ❌ localhost:5000 not responding

echo Testing localhost:9090...
curl -s -o nul -w "%%{http_code}" http://localhost:9090 2>nul && echo ✅ localhost:9090 responds || echo ❌ localhost:9090 not responding

echo.
echo 🌐 Testing Static IP Access:
echo ============================
echo Testing **************:5000...
curl -s -o nul -w "%%{http_code}" http://**************:5000 2>nul && echo ✅ **************:5000 responds || echo ❌ **************:5000 not responding

echo Testing **************:9090...
curl -s -o nul -w "%%{http_code}" http://**************:9090 2>nul && echo ✅ **************:9090 responds || echo ❌ **************:9090 not responding

echo.
echo 🔧 Auto-fixing firewall rules...
netsh advfirewall firewall add rule name="Flask Port 5000 Inbound" dir=in action=allow protocol=TCP localport=5000 >nul 2>&1
netsh advfirewall firewall add rule name="Flask Port 9090 Inbound" dir=in action=allow protocol=TCP localport=9090 >nul 2>&1
echo ✅ Firewall rules added/updated

echo.
echo 📋 TROUBLESHOOTING GUIDE:
echo =========================
echo If external access fails:
echo 1. Check router port forwarding:
echo    - Forward external **************:5000 to this machine's port 5000
echo    - Forward external **************:9090 to this machine's port 9090
echo.
echo 2. Test URLs:
echo    - Local: http://localhost:5000 and http://localhost:9090
echo    - External: http://**************:5000 and http://**************:9090
echo.
echo 3. Check your router/ISP settings for port blocking

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🎉 DEPLOYMENT SUCCESSFUL! 🎉              ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.
echo 🌐 Your Flask application is now running on BOTH servers:
echo.
echo    🔵 PRIMARY SERVER:
echo       ➤ URL: http://**************:5000
echo       ➤ Local: http://localhost:5000
echo       ➤ Status: Running on port 5000
echo.
echo    🟢 BACKUP SERVER:
echo       ➤ URL: http://**************:9090
echo       ➤ Local: http://localhost:9090
echo       ➤ Status: Running on port 9090
echo.
echo 🔑 Login Credentials:
echo    ➤ Admin: pankaj / pankaj123
echo    ➤ Admin: admin / admin123
echo    ➤ User1: user1 / password1
echo    ➤ User2: user2 / password2
echo    ➤ User3: user3 / password3
echo.
echo 📡 API Endpoints (available on both servers):
echo    ➤ GET  /                     - Dashboard
echo    ➤ GET  /login                - Login page
echo    ➤ GET  /dashboard            - Main dashboard
echo    ➤ GET  /events               - Events page
echo    ➤ GET  /api/health           - Health check
echo    ➤ GET  /api/machine-data     - Machine data
echo    ➤ POST /api/machine-data     - Submit data (Arduino)
echo.
echo 🔧 Arduino GSM Module Configuration:
echo    ➤ Primary:  serverURL = "**************", port = 5000
echo    ➤ Backup:   serverURL = "**************", port = 9090
echo.
echo � Benefits of this setup:
echo    ✅ Redundancy - If one server fails, the other continues
echo    ✅ Load balancing - Better performance under load
echo    ✅ Multiple access points - Network flexibility
echo    ✅ Auto-restart - Servers restart if they crash
echo.
echo ⚠️  IMPORTANT NOTES:
echo    ➤ Two separate server windows have opened
echo    ➤ Do NOT close the server windows to keep them running
echo    ➤ You can close this launcher window safely
echo    ➤ Servers will continue running in background
echo.
echo 🔍 To check server status anytime:
echo    ➤ Visit: http://localhost:5000 or http://localhost:9090
echo    ➤ Check: http://localhost:5000/api/health
echo.
echo 📋 Troubleshooting:
echo    ➤ If external access fails, check router port forwarding
echo    ➤ If servers don't start, check the server windows for errors
echo    ➤ If login fails, use credentials listed above
echo.
echo Press any key to close this launcher window...
echo (Flask servers will continue running in separate windows)
pause >nul

echo.
echo 👋 Launcher window closing...
echo 🚀 Flask servers continue running in background
echo 🌐 Access your app at: http://localhost:5000
timeout /t 3 /nobreak >nul