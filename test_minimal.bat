@echo off
echo 🔍 Testing Minimal Flask App
echo =============================

cd /d "C:\inetpub\wwwroot\machine-monitoring\app"
echo ✅ Changed to app directory: %CD%

echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)
echo ✅ Virtual environment activated

echo 🧪 Testing Python...
python --version
if errorlevel 1 (
    echo ❌ Python not found
    pause
    exit /b 1
)

echo 🧪 Testing minimal Flask app...
python test_minimal.py

pause
