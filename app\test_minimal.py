#!/usr/bin/env python3
"""
Minimal test to check if Flask app can start
"""

print("🔍 Testing minimal Flask app startup...")

try:
    # Test 1: Basic imports
    print("1. Testing basic imports...")
    from flask import Flask
    print("   ✅ Flask imported")
    
    from datetime import datetime
    print("   ✅ datetime imported")
    
    import os
    print("   ✅ os imported")
    
    # Test 2: Environment variables
    print("2. Testing environment variables...")
    from dotenv import load_dotenv
    load_dotenv()
    print("   ✅ dotenv loaded")
    
    # Test 3: Supabase import
    print("3. Testing Supabase import...")
    from supabase import create_client, Client
    print("   ✅ Supabase imported")
    
    # Test 4: Create minimal Flask app
    print("4. Creating minimal Flask app...")
    app = Flask(__name__)
    app.secret_key = 'test-key'
    
    @app.route('/')
    def hello():
        return "Hello! Flask app is working!"
    
    @app.route('/health')
    def health():
        return {"status": "ok", "timestamp": datetime.now().isoformat()}
    
    print("   ✅ Minimal Flask app created")
    
    # Test 5: Try to start the app
    print("5. Testing app startup...")
    print("   🚀 Starting Flask app on port 5001...")
    print("   📍 Visit: http://localhost:5001")
    print("   ⏹️  Press Ctrl+C to stop")
    
    app.run(host='0.0.0.0', port=5001, debug=False)
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Missing package. Try: pip install flask python-dotenv supabase")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
