@echo off
echo 🔍 Simple Flask App Startup Test
echo =================================

REM Check admin privileges
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running with Administrator privileges
) else (
    echo ❌ ERROR: This script requires Administrator privileges
    pause
    exit /b 1
)

REM Change to app directory
cd /d "C:\inetpub\wwwroot\machine-monitoring\app"
echo ✅ Changed to directory: %CD%

REM Check if venv exists
if exist "venv\Scripts\activate.bat" (
    echo ✅ Virtual environment found
) else (
    echo ❌ Virtual environment not found
    echo 💡 Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo 🔄 Activating virtual environment...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo ❌ Failed to activate virtual environment
    pause
    exit /b 1
)
echo ✅ Virtual environment activated

REM Check Python
echo 🧪 Testing Python...
python --version
if errorlevel 1 (
    echo ❌ Python not available in virtual environment
    pause
    exit /b 1
)

REM Install/upgrade required packages
echo 📦 Installing required packages...
pip install --upgrade pip
pip install flask python-dotenv supabase waitress

REM Test minimal app first
echo 🧪 Testing minimal Flask app...
python test_minimal.py &

REM Wait a moment
timeout /t 3 /nobreak >nul

REM Test if minimal app is responding
echo 🔍 Testing minimal app response...
curl -s http://localhost:5001 && echo ✅ Minimal app working || echo ❌ Minimal app not responding

echo.
echo 🚀 Now testing full app...
echo Press Ctrl+C to stop and try the full app
pause

REM Try to run the full app
echo 🚀 Starting full Flask app...
python app.py

pause
