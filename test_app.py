#!/usr/bin/env python3
"""
Simple test script to diagnose Flask app startup issues
"""

import sys
import os
import traceback

print("🔍 Testing Flask App Startup...")
print("=" * 50)

# Test 1: Python version
print(f"✅ Python version: {sys.version}")

# Test 2: Check if we're in the right directory
print(f"✅ Current directory: {os.getcwd()}")
print(f"✅ Files in current directory: {os.listdir('.')}")

# Test 3: Check if app directory exists
if os.path.exists('app'):
    print("✅ App directory found")
    print(f"✅ Files in app directory: {os.listdir('app')}")
else:
    print("❌ App directory not found!")
    sys.exit(1)

# Test 4: Try importing required modules
try:
    print("\n🔍 Testing imports...")
    import flask
    print(f"✅ Flask version: {flask.__version__}")
    
    from dotenv import load_dotenv
    print("✅ python-dotenv imported successfully")
    
    from supabase import create_client, Client
    print("✅ Supabase client imported successfully")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Try: pip install flask python-dotenv supabase")
    sys.exit(1)

# Test 5: Try loading environment variables
try:
    print("\n🔍 Testing environment variables...")
    load_dotenv()
    secret_key = os.getenv('SECRET_KEY', 'default-key')
    print(f"✅ SECRET_KEY loaded: {'***' if secret_key != 'default-key' else 'using default'}")
    
except Exception as e:
    print(f"❌ Environment error: {e}")

# Test 6: Try creating Supabase client
try:
    print("\n🔍 Testing Supabase connection...")
    supabase_client = create_client(
        'https://zvfltkbciwppawghqpdl.supabase.co',
        'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA2MDI0NjYsImV4cCI6MjA2NjE3ODQ2Nn0.z_U2VOsbP1lMK88dDp7xmVTLkQQdO9hvI8s47JfpspE'
    )
    print("✅ Supabase client created successfully")
    
    # Test connection
    response = supabase_client.table('machine_events').select("count", count="exact").limit(1).execute()
    print(f"✅ Supabase connection test successful - Records: {response.count}")
    
except Exception as e:
    print(f"❌ Supabase connection failed: {e}")
    print("💡 Check internet connection and Supabase credentials")

# Test 7: Try importing the Flask app
try:
    print("\n🔍 Testing Flask app import...")
    sys.path.insert(0, 'app')
    
    # Import the app module
    import app as flask_app
    print("✅ Flask app module imported successfully")
    
    # Check if app object exists
    if hasattr(flask_app, 'app'):
        print("✅ Flask app object found")
        print(f"✅ App name: {flask_app.app.name}")
    else:
        print("❌ Flask app object not found in module")
    
except Exception as e:
    print(f"❌ Flask app import failed: {e}")
    print("\n📋 Full traceback:")
    traceback.print_exc()

print("\n" + "=" * 50)
print("🏁 Test completed!")
print("💡 If all tests pass, try running: python app/app.py")
