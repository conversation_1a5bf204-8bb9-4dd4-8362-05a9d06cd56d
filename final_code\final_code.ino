#include <ArduinoJson.h>
#include <ThreeWire.h>
#include <RtcDS1302.h>
#include <HardwareSerial.h>
#include <driver/adc.h>
#include <EEPROM.h>

// ========================================
// CONFIGURATION SECTION
// ========================================

// Server Configuration
const char* serverURL = "**************";
const int serverPort = 5000;
const char* apiPath = "/api/machine-data";
const char* apiKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inp2Zmx0a2JjaXdwcGF3Z2hxcGRsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3";

// Machine Identification
// IMPORTANT: Change this for each machine deployment
const char* machineId = "machine_1";  // Options: machine_1, machine_2, machine_3

// GSM Configuration 
const char* apn = "airtelgprs.com";         
const char* gprsUser = "";                 
const char* gprsPass = "";                 
const char* PHONE_NUMBER = "9821216693"; 

// Hardware Pin Configuration
#define CURRENT_SENSOR_PIN ADC1_CHANNEL_6  
#define GSM_TX_PIN 16            
#define GSM_RX_PIN 17            
#define GSM_POWER_PIN 4          
#define LED_PIN 2                
#define BUZZER_PIN 5
#define MACHINE_ON_OFF_PIN 12             

// RTC Pins
#define RTC_IO_PIN 21
#define RTC_SCLK_PIN 22
#define RTC_CE_PIN 19

// EEPROM Configuration
#define EEPROM_SIZE 512
#define EEPROM_ADDR_COUNT 0      // Address to store event count
#define EEPROM_ADDR_DATA 4       // Starting address for event data
#define MAX_EVENTS_STORED 20     // Maximum events to store in EEPROM

// Serial and RTC Objects
HardwareSerial gsmSerial(2);  
ThreeWire rtcWire(RTC_IO_PIN, RTC_SCLK_PIN, RTC_CE_PIN);
RtcDS1302<ThreeWire> rtc(rtcWire);

// Current Sensor Configuration 
const float REF_VOLTAGE = 3.3;    
const int ADC_RESOLUTION = 4095;  
const float BURDEN_RESISTOR = 62.0;    
const float CALIBRATION_FACTOR = 0.95;   
const int NOISE_THRESHOLD = 100;         
const int NUM_SAMPLES = 1000; 

// Machine State Configuration
const float CURRENT_THRESHOLD = 10.0;    

// Timing Configuration
#define READING_INTERVAL 5000
#define SEND_INTERVAL 30000
#define DEBOUNCE_TIME 10000
#define RECONNECT_INTERVAL 60000
#define ALERT_COOLDOWN 30000

// ========================================
// GLOBAL VARIABLES
// ========================================

// Machine State Variables
bool machineState = false;          
bool previousMachineState = false;
unsigned long lastStateChange = 0;  
unsigned long machineStartTime = 0; 
RtcDateTime eventStartTime;
RtcDateTime eventEndTime;

// GPIO Pin 12 Machine Control
bool machineControlPin = false;
bool previousMachineControlPin = false;
unsigned long lastPinChange = 0;

// Timing Variables
unsigned long lastReadingTime = 0;
unsigned long lastReconnectAttempt = 0;
unsigned long lastAlertTime = 0;

// Current Monitoring Variables
float currentReading = 0.0;
float averageCurrent = 0.0;
float maxCurrent = 0.0;
int readingCount = 0;
float currentSum = 0.0;
float offset = 2048; 

// Network Status
bool gprsConnected = false;
bool systemActive = true;

// Event Tracking
bool eventPending = false;
unsigned long eventDuration = 0;

// EEPROM Event Structure
struct MachineEvent {
  uint32_t startTimestamp;
  uint32_t endTimestamp;
  uint32_t duration;
  uint8_t eventType; // 0 = current sensor, 1 = GPIO pin
};

// ========================================
// FUNCTION DECLARATIONS
// ========================================

void initializeRTC();
void initializeGSM();
void initializeEEPROM();
void calibrateCurrentSensor();
void updateStatusLED();
void handleGSMResponses();
void sendSMS(String message);
void sendDataToServer();
void sendHTTPPost(const String& payload);
void printDateTime(const RtcDateTime& dt);
void blinkLED(int times, int delayMs);
String rtcDateTimeToISO(const RtcDateTime& dt);
bool sendATCommand(const char* command, const char* expectedResponse, unsigned long timeout);
String sendATCommandWithResponse(const char* command, unsigned long timeout);
bool waitForResponse(String expected, unsigned long timeout);
String waitForResponseWithReturn(String expected, unsigned long timeout);
String waitForHTTPActionResponse(unsigned long timeout);
void readCurrentSensor();
float getCurrentRMS();
void updateMachineState();
void checkMachineControlPin();
void saveEventToEEPROM(const MachineEvent& event);
void loadEventsFromEEPROM();
void printStoredEvents();
uint32_t rtcToTimestamp(const RtcDateTime& dt);
RtcDateTime timestampToRtc(uint32_t timestamp);

// ========================================
// SETUP FUNCTION
// ========================================

void setup() {
  Serial.begin(115200);
  while (!Serial); 
  Serial.println("\n\n========================================");
  Serial.println("Machine Monitoring System with GPIO Control");
  Serial.println("========================================");
  
  // Initialize pins
  pinMode(LED_PIN, OUTPUT);
  pinMode(BUZZER_PIN, OUTPUT);
  pinMode(GSM_POWER_PIN, OUTPUT);
  pinMode(MACHINE_ON_OFF_PIN, INPUT_PULLUP); // GPIO 12 as input with pullup
  
  // Configure ADC for current sensor
  adc1_config_width(ADC_WIDTH_BIT_12);
  adc1_config_channel_atten(ADC1_CHANNEL_6, ADC_ATTEN_DB_12);
  
  // Initial LED pattern
  blinkLED(3, 200);
  
  // Initialize EEPROM
  initializeEEPROM();
  
  // Initialize RTC
  initializeRTC();
  
  // Initialize GSM 
  initializeGSM();
  
  // Calibrate current sensor
  calibrateCurrentSensor();
  
  // Read initial state of GPIO pin 12
  machineControlPin = digitalRead(MACHINE_ON_OFF_PIN);
  previousMachineControlPin = machineControlPin;
  Serial.printf("🔧 Initial GPIO 12 state: %s\n", machineControlPin ? "HIGH (Machine ON)" : "LOW (Machine OFF)");
  
  // Load and display stored events
  loadEventsFromEEPROM();
  
  Serial.println("🚀 System initialization complete!");
  Serial.println("📊 Starting machine monitoring...");
  Serial.println("🔧 GPIO 12 Machine Control: ACTIVE");
  
  // Ready indication
  blinkLED(5, 100);
  digitalWrite(LED_PIN, HIGH);
  
  // Initialize machine state
  machineState = false;
  lastStateChange = millis();
}

// ========================================
// MAIN LOOP
// ========================================

void loop() {
  unsigned long currentTime = millis();
  
  // Check GPIO pin 12 for machine control
  checkMachineControlPin();
  
  // Read current sensor (only if GPIO allows or for monitoring)
  if (currentTime - lastReadingTime >= READING_INTERVAL) {
    readCurrentSensor();
    updateMachineState();
    lastReadingTime = currentTime;
  }
  
  // Send data to server
  if (eventPending) {
    Serial.println("📤 Event pending - sending to server...");
    sendDataToServer();
    eventPending = false; 
  }
  
  // Check GSM connection
  if (!gprsConnected && (currentTime - lastReconnectAttempt >= RECONNECT_INTERVAL)) {
    Serial.println("🔄 Attempting GSM reconnection...");
    initializeGSM();
    lastReconnectAttempt = currentTime;
  }
  
  // Update status LED
  updateStatusLED();
  
  // Handle GSM responses
  handleGSMResponses();
  
  delay(100);
}

// ========================================
// INITIALIZATION FUNCTIONS
// ========================================

void initializeEEPROM() {
  Serial.println("💾 Initializing EEPROM...");
  
  if (!EEPROM.begin(EEPROM_SIZE)) {
    Serial.println("❌ Failed to initialize EEPROM");
    return;
  }
  
  // Check if EEPROM is initialized (first run)
  uint32_t eventCount;
  EEPROM.get(EEPROM_ADDR_COUNT, eventCount);
  
  if (eventCount == 0xFFFFFFFF) { // EEPROM not initialized
    Serial.println("🔧 First run - initializing EEPROM");
    eventCount = 0;
    EEPROM.put(EEPROM_ADDR_COUNT, eventCount);
    EEPROM.commit();
  }
  
  Serial.printf("✅ EEPROM initialized - %u events stored\n", eventCount);
}

void initializeRTC() {
  Serial.println("🕒 Initializing DS1302 RTC...");
  
  rtc.Begin();
  
  // Disable write protection and start RTC
  rtc.SetIsWriteProtected(false);
  rtc.SetIsRunning(true);
  
  // Check if RTC has valid time
  if (!rtc.IsDateTimeValid()) {
    Serial.println("❌ RTC lost confidence in the DateTime!");
    
    // Set default time
    RtcDateTime compiled = RtcDateTime(__DATE__, __TIME__);
    rtc.SetDateTime(compiled);
    Serial.println("⏰ RTC set to compilation time");
  }
  
  // Print current RTC time
  RtcDateTime now = rtc.GetDateTime();
  printDateTime(now);
  Serial.println("✅ RTC initialized");
}

void initializeGSM() {
  Serial.println("📱 Initializing A7670 GSM module ...");
  
  // Hardware reset
  digitalWrite(GSM_POWER_PIN, LOW);
  delay(1000);
  digitalWrite(GSM_POWER_PIN, HIGH);
  delay(5000);
  
  // Initialize serial
  gsmSerial.begin(115200, SERIAL_8N1, GSM_RX_PIN, GSM_TX_PIN);
  delay(1000);
  while (gsmSerial.available()) gsmSerial.read();
  
  // Basic AT test
  if (!sendATCommand("AT", "OK", 3000)) {
    Serial.println("❌ GSM module not responding");
    systemActive = false;
    return;
  }
  Serial.println("✅ GSM module responding");
  
  // Configure basic settings
  sendATCommand("ATE0", "OK", 3000);
  sendATCommand("AT+CMEE=2", "OK", 3000);
  
  // Check SIM
  if (!sendATCommand("AT+CPIN?", "READY", 3000)) {
    Serial.println("❌ SIM not ready");
    systemActive = false;
    return;
  }
  Serial.println("✅ SIM ready");
  
  // Check network registration
  String registration = sendATCommandWithResponse("AT+CREG?", 3000);
  if (registration.indexOf("+CREG: 0,1") == -1 && registration.indexOf("+CREG: 0,5") == -1) {
    Serial.println("❌ Not registered to network");
    systemActive = false;
    return;
  }
  Serial.println("✅ Network registered");
  
  // GPRS attach
  if (!sendATCommand("AT+CGATT=1", "OK", 10000)) {
    Serial.println("❌ GPRS attach failed");
    systemActive = false;
    return;
  }
  Serial.println("✅ GPRS attached");
  
  // Configure PDP context
  String pdpCommand = "AT+CGDCONT=1,\"IP\",\"" + String(apn) + "\"";
  if (!sendATCommand(pdpCommand.c_str(), "OK", 5000)) {
    Serial.println("❌ PDP context configuration failed");
    systemActive = false;
    return;
  }
  Serial.println("✅ PDP context configured");
  
  // Activate PDP context
  if (!sendATCommand("AT+CGACT=1,1", "OK", 15000)) {
    Serial.println("❌ PDP context activation failed");
    systemActive = false;
    return;
  }
  Serial.println("✅ PDP context active");
  
  // Get IP address
  String ipResponse = sendATCommandWithResponse("AT+CGPADDR=1", 5000);
  Serial.println("🌐 IP response: " + ipResponse);
  
  gprsConnected = true;
  systemActive = true;
  
  Serial.println("🎉 GSM initialization completed !");
}

void calibrateCurrentSensor() {
  Serial.println("⚡ Calibrating current sensor...");
  float sum = 0;
  for (int i = 0; i < 1000; i++) {
    sum += adc1_get_raw(ADC1_CHANNEL_6);
    delay(1);
  }
  offset = sum / 1000.0;
  Serial.printf("✅ Calibrated Offset: %.2f\n", offset);
}

// ========================================
// GPIO PIN 12 MACHINE CONTROL
// ========================================

void checkMachineControlPin() {
  machineControlPin = digitalRead(MACHINE_ON_OFF_PIN);
  
  // Check for state change with debouncing
  if (machineControlPin != previousMachineControlPin) {
    if (millis() - lastPinChange > 100) { // 100ms debounce
      lastPinChange = millis();
      
      RtcDateTime currentTime = rtc.GetDateTime();
      MachineEvent event;
      
      if (machineControlPin) {
        // Pin went HIGH - Machine turned ON
        Serial.println("🔧 GPIO 12: HIGH - Machine turned ON via GPIO control");
        Serial.print("   ON time: ");
        printDateTime(currentTime);
        
        // Store start time
        eventStartTime = currentTime;
        machineStartTime = millis();
        
        // Create event for turning ON (we'll update duration when it turns OFF)
        event.startTimestamp = rtcToTimestamp(currentTime);
        event.endTimestamp = 0; // Will be updated when machine turns OFF
        event.duration = 0;
        event.eventType = 1; // GPIO pin event
        
      } else {
        // Pin went LOW - Machine turned OFF
        unsigned long duration = (millis() - machineStartTime) / 1000;
        eventEndTime = currentTime;
        
        Serial.println("🔧 GPIO 12: LOW - Machine turned OFF via GPIO control");
        Serial.printf("   OFF time: ");
        printDateTime(currentTime);
        Serial.printf("   Duration: %lu seconds\n", duration);
        
        // Create complete event
        event.startTimestamp = rtcToTimestamp(eventStartTime);
        event.endTimestamp = rtcToTimestamp(eventEndTime);
        event.duration = duration;
        event.eventType = 1; // GPIO pin event
        
        // Save to EEPROM
        saveEventToEEPROM(event);
        
        // Set event pending for server transmission
        if (duration >= 5) {
          eventDuration = duration;
          eventPending = true;
          Serial.println("   ✅ GPIO Event ready to send to server!");
        } else {
          Serial.println("   ⚠️ GPIO Duration too short, skipping server transmission");
        }
      }
      
      previousMachineControlPin = machineControlPin;
    }
  }
}

// ========================================
// EEPROM FUNCTIONS
// ========================================

void saveEventToEEPROM(const MachineEvent& event) {
  Serial.println("💾 Saving event to EEPROM...");
  
  // Get current event count
  uint32_t eventCount;
  EEPROM.get(EEPROM_ADDR_COUNT, eventCount);
  
  // Calculate address for new event
  int eventIndex = eventCount % MAX_EVENTS_STORED; // Circular buffer
  int eventAddr = EEPROM_ADDR_DATA + (eventIndex * sizeof(MachineEvent));
  
  // Save event
  EEPROM.put(eventAddr, event);
  
  // Update event count
  eventCount++;
  EEPROM.put(EEPROM_ADDR_COUNT, eventCount);
  
  // Commit to EEPROM
  EEPROM.commit();
  
  Serial.printf("✅ Event saved to EEPROM (Index: %d, Total: %u)\n", eventIndex, eventCount);
  Serial.printf("   Start: %u, End: %u, Duration: %u sec, Type: %s\n", 
                event.startTimestamp, event.endTimestamp, event.duration,
                event.eventType == 0 ? "Current Sensor" : "GPIO Pin");
}

void loadEventsFromEEPROM() {
  Serial.println("💾 Loading events from EEPROM...");
  
  uint32_t eventCount;
  EEPROM.get(EEPROM_ADDR_COUNT, eventCount);
  
  if (eventCount == 0) {
    Serial.println("📝 No events stored in EEPROM");
    return;
  }
  
  Serial.printf("📝 Found %u events in EEPROM\n", eventCount);
  
  // Display last few events
  int eventsToShow = min((int)eventCount, 5);
  Serial.printf("📋 Showing last %d events:\n", eventsToShow);
  
  for (int i = 0; i < eventsToShow; i++) {
    int eventIndex = ((eventCount - eventsToShow + i) % MAX_EVENTS_STORED);
    int eventAddr = EEPROM_ADDR_DATA + (eventIndex * sizeof(MachineEvent));
    
    MachineEvent event;
    EEPROM.get(eventAddr, event);
    
    Serial.printf("  Event %d: ", i + 1);
    Serial.printf("Start: ");
    printDateTime(timestampToRtc(event.startTimestamp));
    Serial.printf("    End: ");
    printDateTime(timestampToRtc(event.endTimestamp));
    Serial.printf("    Duration: %u sec, Type: %s\n", 
                  event.duration, event.eventType == 0 ? "Current" : "GPIO");
  }
}

void printStoredEvents() {
  loadEventsFromEEPROM();
}

// ========================================
// TIMESTAMP CONVERSION FUNCTIONS
// ========================================

uint32_t rtcToTimestamp(const RtcDateTime& dt) {
  // Simple timestamp: seconds since 2000-01-01
  // This is a simplified version - you might want to use proper Unix timestamp
  uint32_t days = 0;
  
  // Add days for years since 2000
  for (int year = 2000; year < dt.Year(); year++) {
    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
      days += 366; // Leap year
    } else {
      days += 365;
    }
  }
  
  // Add days for months in current year
  int daysInMonth[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
  if ((dt.Year() % 4 == 0 && dt.Year() % 100 != 0) || (dt.Year() % 400 == 0)) {
    daysInMonth[1] = 29; // Leap year
  }
  
  for (int month = 1; month < dt.Month(); month++) {
    days += daysInMonth[month - 1];
  }
  
  // Add days in current month
  days += dt.Day() - 1;
  
  // Convert to seconds and add time
  uint32_t timestamp = days * 86400UL + dt.Hour() * 3600UL + dt.Minute() * 60UL + dt.Second();
  
  return timestamp;
}

RtcDateTime timestampToRtc(uint32_t timestamp) {
  // Convert timestamp back to RtcDateTime
  // This is a simplified reverse conversion
  uint32_t totalDays = timestamp / 86400UL;
  uint32_t remainingSeconds = timestamp % 86400UL;
  
  int year = 2000;
  while (totalDays > 365) {
    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
      if (totalDays >= 366) {
        totalDays -= 366;
        year++;
      } else {
        break;
      }
    } else {
      totalDays -= 365;
      year++;
    }
  }
  
  int daysInMonth[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
  if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
    daysInMonth[1] = 29;
  }
  
  int month = 1;
  while (totalDays >= daysInMonth[month - 1]) {
    totalDays -= daysInMonth[month - 1];
    month++;
  }
  
  int day = totalDays + 1;
  int hour = remainingSeconds / 3600;
  int minute = (remainingSeconds % 3600) / 60;
  int second = remainingSeconds % 60;
  
  return RtcDateTime(year, month, day, hour, minute, second);
}

// ========================================
// GSM HELPER FUNCTIONS
// ========================================

bool sendATCommand(const char* command, const char* expectedResponse, unsigned long timeout) {
  // Clear serial buffer
  while (gsmSerial.available()) gsmSerial.read();
  
  // Send command
  gsmSerial.println(command);
  Serial.println(">> " + String(command));
  
  // Wait for response
  unsigned long start = millis();
  String response = "";
  
  while (millis() - start < timeout) {
    while (gsmSerial.available()) {
      char c = gsmSerial.read();
      response += c;
      
      // Check for expected response
      if (response.indexOf(expectedResponse) != -1) {
        Serial.print("<< ");
        Serial.println(response);
        return true;
      }
    }
  }
  
  Serial.print("<< [Timeout] ");
  Serial.println(response);
  return false;
}

String sendATCommandWithResponse(const char* command, unsigned long timeout) {
  // Clear serial buffer
  while (gsmSerial.available()) gsmSerial.read();
  
  // Send command
  gsmSerial.println(command);
  Serial.println(">> " + String(command));
  
  // Wait for response
  unsigned long start = millis();
  String response = "";
  
  while (millis() - start < timeout) {
    while (gsmSerial.available()) {
      char c = gsmSerial.read();
      response += c;
    }
    
    // If we have a complete response
    if (response.length() > 0 && millis() - start > 1000) {
      Serial.print("<< ");
      Serial.println(response);
      return response;
    }
  }
  
  Serial.print("<< [Timeout] ");
  Serial.println(response);
  return response;
}

void sendSMS(String message) {
  if(millis() - lastAlertTime < ALERT_COOLDOWN) {
    return;
  }
  
  Serial.println("📱 Sending SMS: " + message);
  
  // Clear buffer
  while (gsmSerial.available()) gsmSerial.read();
  
  gsmSerial.print("AT+CMGS=\"");
  gsmSerial.print(PHONE_NUMBER);
  gsmSerial.println("\"");
  
  // Wait for prompt
  if (!waitForResponse(">", 3000)) {
    Serial.println("❌ SMS prompt not received");
    return;
  }
  
  // Send message content
  gsmSerial.print(message);
  gsmSerial.write(26); // CTRL+Z
  
  // Wait for confirmation
  if (waitForResponse("+CMGS:", 10000)) {
    lastAlertTime = millis();
    Serial.println("✅ SMS sent successfully");
  } else {
    Serial.println("❌ SMS failed");
  }
}

bool waitForResponse(String expected, unsigned long timeout) {
  unsigned long start = millis();
  String response = "";
  
  while (millis() - start < timeout) {
    while (gsmSerial.available()) {
      char c = gsmSerial.read();
      response += c;
      
      if (response.indexOf(expected) != -1) {
        Serial.print("<< ");
        Serial.println(response);
        return true;
      }
    }
  }
  
  Serial.print("<< [Timeout waiting for '");
  Serial.print(expected);
  Serial.print("'] ");
  Serial.println(response);
  return false;
}

String waitForResponseWithReturn(String expected, unsigned long timeout) {
  unsigned long start = millis();
  String response = "";
  
  while (millis() - start < timeout) {
    while (gsmSerial.available()) {
      char c = gsmSerial.read();
      response += c;
      Serial.print(c); // Print each character as received
      
      if (response.indexOf(expected) != -1) {
        Serial.println("\n<< Found: " + expected);
        return response;
      }
    }
  }
  
  Serial.println("\n<< [Timeout waiting for '" + expected + "']");
  Serial.println("Full response: " + response);
  return response;
}

// NEW: Specialized function for HTTPACTION response
String waitForHTTPActionResponse(unsigned long timeout) {
  unsigned long start = millis();
  String response = "";
  String currentLine = "";
  
  Serial.println("🔍 Waiting for +HTTPACTION response...");
  Serial.println("Raw data stream:");
  
  while (millis() - start < timeout) {
    while (gsmSerial.available()) {
      char c = gsmSerial.read();
      Serial.print(c); // Show every character
      response += c;
      
      // Build current line
      if (c == '\n' || c == '\r') {
        if (currentLine.length() > 0) {
          currentLine.trim();
          Serial.println("\n[LINE]: '" + currentLine + "'");
          
          // Check if this line contains +HTTPACTION
          if (currentLine.indexOf("+HTTPACTION:") != -1) {
            Serial.println("✅ Found HTTPACTION line: " + currentLine);
            return currentLine; // Return just the HTTPACTION line
          }
          currentLine = "";
        }
      } else {
        currentLine += c;
      }
    }
    delay(10); // Small delay to prevent overwhelming
  }
  
  // Check final line if no newline at end
  if (currentLine.length() > 0) {
    currentLine.trim();
    Serial.println("\n[FINAL LINE]: '" + currentLine + "'");
    if (currentLine.indexOf("+HTTPACTION:") != -1) {
      Serial.println("✅ Found HTTPACTION in final line: " + currentLine);
      return currentLine;
    }
  }
  
  Serial.println("\n❌ No +HTTPACTION found in response");
  Serial.println("Complete response was:");
  Serial.println("'" + response + "'");
  return "";
}

void handleGSMResponses() {
  static String buffer = "";
  
  while (gsmSerial.available()) {
    char c = gsmSerial.read();
    buffer += c;
    
    // Check for line endings
    if (c == '\n') {
      buffer.trim();
      if (buffer.length() > 0) {
        Serial.println("<< " + buffer);
        
        // Check for incoming SMS notifications
        if (buffer.startsWith("+CMT:")) {
          Serial.println("📨 New SMS received!");
        }
      }
      buffer = "";
    }
  }
}

// ========================================
// CURRENT MONITORING
// ========================================

void readCurrentSensor() {
  // Get real RMS current from sensor
  float realCurrent = getCurrentRMS();

  // ========================================
  // DEMO/SIMULATION CODE - COMMENTED OUT FOR REAL DATA TESTING
  // ========================================
  /*
  // Add simulation for testing (only if GPIO pin allows)
  static unsigned long lastStateToggle = 0;
  static bool simulatedMachineOn = false;

  // Auto-toggle machine state every 45 seconds for testing (only if GPIO is HIGH)
  if (machineControlPin && millis() - lastStateToggle > 45000) {
    simulatedMachineOn = !simulatedMachineOn;
    lastStateToggle = millis();
    Serial.printf("🔄 Simulated machine state changed to: %s (GPIO allows operation)\n", simulatedMachineOn ? "ON" : "OFF");
  }

  // Use real current if significant, otherwise use simulation (only if GPIO allows)
  if (realCurrent > 1.0) {
    currentReading = realCurrent;
  } else {
    if (machineControlPin && simulatedMachineOn) {
      currentReading = 15.0 + random(-200, 200) / 100.0;
    } else {
      currentReading = 0.5 + random(-30, 30) / 100.0;
    }
  }
  */

  // ========================================
  // REAL SENSOR READING ONLY - FOR TESTING
  // ========================================

  // Use only real current sensor values
  currentReading = realCurrent;

  // Debug: Show raw sensor reading for testing
  Serial.printf("🔍 RAW Sensor Reading: %.3fA | GPIO Pin: %s\n", realCurrent, machineControlPin ? "HIGH" : "LOW");

  // Update statistics
  currentSum += currentReading;
  readingCount++;

  if (currentReading > maxCurrent) {
    maxCurrent = currentReading;
  }

  // Calculate average and display real values only
  if (readingCount >= 3) {
    averageCurrent = currentSum / readingCount;
    currentSum = 0;
    readingCount = 0;

    Serial.printf("⚡ REAL Current: %.2fA (Avg: %.2fA, Max: %.2fA) - Machine: %s\n",
                  currentReading, averageCurrent, maxCurrent,
                  (currentReading > CURRENT_THRESHOLD) ? "ON" : "OFF");

    // Additional debug info for testing real sensor
    if (currentReading > CURRENT_THRESHOLD) {
      Serial.println("🟢 Machine detected as ON based on REAL current sensor");
    } else {
      Serial.println("🔴 Machine detected as OFF based on REAL current sensor");
    }

    // Show threshold comparison for debugging
    Serial.printf("🎯 Current Threshold: %.1fA | Current Reading: %.2fA\n", CURRENT_THRESHOLD, currentReading);
  }
}

float getCurrentRMS() {
  float sum_sq = 0;
  int sample_count = 0;
  
  unsigned long start_time = millis();
  while (sample_count < NUM_SAMPLES) {
    int raw = adc1_get_raw(ADC1_CHANNEL_6);
    float sample = raw - offset;
    
    if (abs(sample) > NOISE_THRESHOLD) {
      sum_sq += sample * sample;
      sample_count++;
    }
    
    // Timeout protection
    if (millis() - start_time > 2000) break;
  }
  
  if(sample_count == 0) return 0.0;
  
  // Calculate RMS values
  float mean = sum_sq / sample_count;
  float rms_adc = sqrt(mean);
  float rms_voltage = (rms_adc * REF_VOLTAGE) / ADC_RESOLUTION;
  float rms_current = (rms_voltage / BURDEN_RESISTOR) * CALIBRATION_FACTOR;
  
  return rms_current;
}

void updateMachineState() {
  // Detect state based on current threshold (only if GPIO allows)
  bool newState = (averageCurrent > CURRENT_THRESHOLD) && machineControlPin;
  
  if (newState != machineState) {
    machineState = newState;
    
    if (machineState) {
      // Machine turned ON (current sensor + GPIO)
      machineStartTime = millis();
      eventStartTime = rtc.GetDateTime();
      Serial.println("🟢 MACHINE STATE DETECTED: ON (Current Sensor + GPIO)");
      Serial.print("   Start time: ");
      printDateTime(eventStartTime);
    } else {
      // Machine turned OFF (current sensor)
      eventEndTime = rtc.GetDateTime();
      unsigned long duration = (millis() - machineStartTime) / 1000;
      Serial.printf("🔴 MACHINE STATE DETECTED: OFF (Duration: %lu seconds)\n", duration);
      Serial.print("   Start: ");
      printDateTime(eventStartTime);
      Serial.print("   End: ");
      printDateTime(eventEndTime);
      
      if (duration >= 5) {
        // Save current sensor event to EEPROM
        MachineEvent event;
        event.startTimestamp = rtcToTimestamp(eventStartTime);
        event.endTimestamp = rtcToTimestamp(eventEndTime);
        event.duration = duration;
        event.eventType = 0; // Current sensor event
        saveEventToEEPROM(event);
        
        eventDuration = duration;
        eventPending = true;
        Serial.println("   ✅ Current Sensor Event ready to send!");
      } else {
        Serial.println("   ⚠️ Duration too short, skipping event");
      }
    }
  }
}

// ========================================
// DATA TRANSMISSION - EXACT SIMCOM SEQUENCE
// ========================================

void sendDataToServer() {
  if (!gprsConnected) {
    Serial.println("❌ GPRS not connected, cannot send data");
    return;
  }
  
  // Create JSON payload
  StaticJsonDocument<256> doc;
  String startTime = rtcDateTimeToISO(eventStartTime);
  String endTime = rtcDateTimeToISO(eventEndTime);
  doc["start_time"] = startTime;
  doc["end_time"] = endTime;
  doc["duration"] = eventDuration;
  doc["api_key"] = apiKey;
  doc["machine_id"] = machineId;  // Use configurable machine ID
  doc["control_method"] = machineControlPin ? "GPIO_PIN" : "CURRENT_SENSOR";

  String jsonString;
  serializeJson(doc, jsonString);

  Serial.println("📦 JSON Payload for Machine: " + String(machineId));
  Serial.println(jsonString);
  Serial.println("📏 Payload size: " + String(jsonString.length()) + " bytes");
  Serial.println("🏭 Machine ID: " + String(machineId) + " (User will see only this machine's data)");

  // Send using EXACT SIMCom HTTP sequence
  sendHTTPPost(jsonString);
}

String rtcDateTimeToISO(const RtcDateTime& dt) {
  char buffer[30];  // Increased buffer size
  snprintf(buffer, sizeof(buffer), "%04d-%02d-%02dT%02d:%02d:%02d.000Z",
           dt.Year(), dt.Month(), dt.Day(),
           dt.Hour(), dt.Minute(), dt.Second());
  return String(buffer);
}

void sendHTTPPost(const String& payload) {
  Serial.println("📡 STARTING SIMCOM HTTP POST SEQUENCE");
  Serial.println("========================================");
  
  // STEP 1: AT+HTTPINIT - Start HTTP service, activate PDP context
  Serial.println("🔍 STEP 1: AT+HTTPINIT");
  if (!sendATCommand("AT+HTTPINIT", "OK", 10000)) {
    Serial.println("❌ HTTP init failed");
    return;
  }
  Serial.println("✅ HTTP service started");
  delay(1000);
  
  // STEP 2: AT+HTTPPARA="URL" - Set the URL 
  Serial.println("🔍 STEP 2: Setting URL");
  String url = "http://" + String(serverURL) + ":" + String(serverPort) + String(apiPath);
  String urlCommand = "AT+HTTPPARA=\"URL\",\"" + url + "\"";
  
  Serial.println("URL: " + url);
  if (!sendATCommand(urlCommand.c_str(), "OK", 5000)) {
    Serial.println("❌ URL setting failed");
    sendATCommand("AT+HTTPTERM", "OK", 3000);
    return;
  }
  Serial.println("✅ URL set");
  delay(500);
  
  // STEP 3: Set Content-Type
  Serial.println("🔍 STEP 3: Setting Content-Type");
  if (!sendATCommand("AT+HTTPPARA=\"CONTENT\",\"application/json\"", "OK", 5000)) {
    Serial.println("❌ Content-Type failed");
    sendATCommand("AT+HTTPTERM", "OK", 3000);
    return;
  }
  Serial.println("✅ Content-Type set");
  delay(500);
  
  // STEP 4: AT+HTTPDATA - Prepare to send data
  Serial.println("🔍 STEP 4: AT+HTTPDATA");
  String dataCommand = "AT+HTTPDATA=" + String(payload.length()) + ",10000";
  Serial.println("Command: " + dataCommand);
  Serial.println("Payload length: " + String(payload.length()) + " bytes");
  
  // Clear buffer
  while (gsmSerial.available()) gsmSerial.read();
  
  // Send HTTPDATA command
  gsmSerial.println(dataCommand);
  Serial.println(">> " + dataCommand);
  
  // STEP 5: Wait for DOWNLOAD prompt - CRITICAL STEP
  Serial.println("🔍 STEP 5: Waiting for DOWNLOAD prompt...");
  String downloadResponse = waitForResponseWithReturn("DOWNLOAD", 15000);
  
  if (downloadResponse.indexOf("DOWNLOAD") == -1) {
    Serial.println("❌ DOWNLOAD prompt not received!");
    Serial.println("Response was: " + downloadResponse);
    sendATCommand("AT+HTTPTERM", "OK", 3000);
    return;
  }
  Serial.println("✅ DOWNLOAD prompt received!");
  delay(500);
  
  // STEP 6: Send JSON payload
  Serial.println("🔍 STEP 6: Sending JSON payload");
  Serial.println("Payload: " + payload);
  
  // Send payload byte by byte with small delays
  for (int i = 0; i < payload.length(); i++) {
    gsmSerial.write(payload[i]);
    delay(1); // Small delay between bytes
  }
  
  Serial.println("\n📤 Payload sent!");
  
  // STEP 7: Wait for OK after data
  Serial.println("🔍 STEP 7: Waiting for data acceptance...");
  String dataResponse = waitForResponseWithReturn("OK", 15000);
  
  if (dataResponse.indexOf("OK") == -1) {
    Serial.println("❌ Data not accepted!");
    Serial.println("Response: " + dataResponse);
    sendATCommand("AT+HTTPTERM", "OK", 3000);
    return;
  }
  Serial.println("✅ Data accepted!");
  delay(1000);
  
  // STEP 8: AT+HTTPACTION=1 - Execute POST
  Serial.println("🔍 STEP 8: AT+HTTPACTION=1");
  
  // Clear buffer before action
  while (gsmSerial.available()) gsmSerial.read();
  
  gsmSerial.println("AT+HTTPACTION=1");
  Serial.println(">> AT+HTTPACTION=1");
  
  // STEP 9: Wait for +HTTPACTION response - MOST CRITICAL
  Serial.println("🔍 STEP 9: Waiting for +HTTPACTION response...");
  
  // Use specialized function for better parsing
  String httpActionLine = waitForHTTPActionResponse(30000);
  
  if (httpActionLine.length() == 0) {
    Serial.println("❌ No +HTTPACTION response received!");
    sendATCommand("AT+HTTPTERM", "OK", 3000);
    return;
  }
  
  // STEP 10: Parse the HTTPACTION response
  Serial.println("🔍 STEP 10: Parsing HTTP response");
  Serial.println("========================================");
  Serial.println("HTTPACTION Line: '" + httpActionLine + "'");
  
  // Parse: +HTTPACTION: 1,200,45
  // Find the colon
  int colonPos = httpActionLine.indexOf(':');
  if (colonPos == -1) {
    Serial.println("❌ Invalid HTTPACTION format - no colon found");
    sendATCommand("AT+HTTPTERM", "OK", 3000);
    return;
  }
  
  // Get everything after the colon
  String params = httpActionLine.substring(colonPos + 1);
  params.trim();
  Serial.println("Parameters: '" + params + "'");
  
  // Split by commas: should be "1,status,length"
  int firstComma = params.indexOf(',');
  int secondComma = params.indexOf(',', firstComma + 1);
  
  if (firstComma == -1 || secondComma == -1) {
    Serial.println("❌ Invalid HTTPACTION format - missing commas");
    Serial.println("Expected format: +HTTPACTION: 1,status,length");
    sendATCommand("AT+HTTPTERM", "OK", 3000);
    return;
  }
  
  String method = params.substring(0, firstComma);
  String httpStatus = params.substring(firstComma + 1, secondComma);
  String responseLength = params.substring(secondComma + 1);
  
  // Clean up the strings
  method.trim();
  httpStatus.trim();
  responseLength.trim();
  
  Serial.println("🔍 Parsed values:");
  Serial.println("  Method: '" + method + "'");
  Serial.println("  Status: '" + httpStatus + "'");
  Serial.println("  Length: '" + responseLength + "'");
  
  // Convert status to integer
  int statusCode = httpStatus.toInt();
  
  if (statusCode == 0 && httpStatus != "0") {
    Serial.println("❌ Failed to parse HTTP status code: '" + httpStatus + "'");
    sendATCommand("AT+HTTPTERM", "OK", 3000);
    return;
  }
  
  // STEP 11: Analyze HTTP status
  Serial.println("🔍 STEP 11: HTTP Status Analysis");
  Serial.println("📊 HTTP Status Code: " + String(statusCode));
  
  if (statusCode == 200) {
    Serial.println("🎉 SUCCESS! HTTP 200 - Data sent successfully!");
  } else if (statusCode == 201) {
    Serial.println("🎉 SUCCESS! HTTP 201 - Data created successfully!");
  } else if (statusCode == 400) {
    Serial.println("❌ HTTP 400 - Bad Request (check JSON format)");
  } else if (statusCode == 401) {
    Serial.println("❌ HTTP 401 - Unauthorized (check API key)");
  } else if (statusCode == 404) {
    Serial.println("❌ HTTP 404 - Not Found (check URL/endpoint)");
  } else if (statusCode == 500) {
    Serial.println("❌ HTTP 500 - Server Error");
  } else if (statusCode > 0) {
    Serial.println("⚠️ HTTP " + String(statusCode) + " - Unexpected status code");
  } else {
    Serial.println("❌ Invalid HTTP status code: " + String(statusCode));
  }
  
  // STEP 12: Read response if available
  int respLength = responseLength.toInt();
  if (respLength > 0) {
    Serial.println("🔍 STEP 12: Reading server response (" + String(respLength) + " bytes)");
    String readResponse = sendATCommandWithResponse("AT+HTTPREAD", 10000);
    Serial.println("📥 Server response: " + readResponse);
  }
  
  // STEP 13: Terminate HTTP service
  Serial.println("🔍 STEP 13: AT+HTTPTERM");
  if (sendATCommand("AT+HTTPTERM", "OK", 5000)) {
    Serial.println("✅ HTTP service terminated");
  } else {
    Serial.println("⚠️ HTTP termination failed");
  }
  
  Serial.println("========================================");
  Serial.println("📡 SIMCom HTTP POST sequence completed!");
}

// ========================================
// UTILITY FUNCTIONS
// ========================================

void blinkLED(int times, int delayMs) {
  for (int i = 0; i < times; i++) {
    digitalWrite(LED_PIN, HIGH);
    delay(delayMs);
    digitalWrite(LED_PIN, LOW);
    delay(delayMs);
  }
}

void updateStatusLED() {
  static unsigned long lastBlink = 0;
  static bool ledState = false;
  
  if (gprsConnected) {
    // Show GPIO state on LED
    if (machineControlPin) {
      digitalWrite(LED_PIN, eventPending ? LOW : HIGH); 
    } else {
      // Blink slowly when GPIO LOW
      if (millis() - lastBlink > 1000) {
        ledState = !ledState;
        digitalWrite(LED_PIN, ledState);
        lastBlink = millis();
      }
    }
  } else {
    // Fast blink when no GPRS
    if (millis() - lastBlink > 500) {
      ledState = !ledState;
      digitalWrite(LED_PIN, ledState);
      lastBlink = millis();
    }
  }
}

void printDateTime(const RtcDateTime& dt) {
  Serial.printf("📅 %04d-%02d-%02d %02d:%02d:%02d\n",
                dt.Year(), dt.Month(), dt.Day(),
                dt.Hour(), dt.Minute(), dt.Second());
}
