/* <PERSON><PERSON> Styling for Better Visibility */
.navbar-logo {
    transition: all 0.3s ease !important;
    border-radius: 8px !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15) !important;
    background: rgba(255,255,255,0.1) !important;
    padding: 5px !important;
}

.navbar-logo:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.25) !important;
    background: rgba(255,255,255,0.15) !important;
}

.login-logo {
    transition: all 0.3s ease !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 16px rgba(0,0,0,0.2) !important;
    max-height: 120px !important;
    max-width: 300px !important;
}

.login-logo:hover {
    transform: scale(1.05) !important;
    box-shadow: 0 8px 24px rgba(0,0,0,0.3) !important;
}

.logo-wrapper {
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255,255,255,0.2) !important;
    background: rgba(255,255,255,0.15) !important;
}

.logo-fallback {
    color: white !important;
    font-weight: bold !important;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
}

:root {
    --primary: #2c3e50;
    --secondary: #3498db;
    --success: #27ae60;
    --info: #17a2b8;
    --warning: #f39c12;
    --danger: #e74c3c;
    --light: #f8f9fa;
    --dark: #343a40;
    --on-color: #d4edda;
    --off-color: #f8d7da;
}

body {
    background-color: #f5f7fa;
    color: #333;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.navbar {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    background-color: var(--primary) !important;
}

.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    margin-bottom: 1.5rem;
    border: none;
}

.card:hover {
    transform: translateY(-5px);
}

.card-header {
    font-weight: 600;
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
    background-color: var(--primary);
    color: white;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

#total-runtime, #total-events {
    color: var(--primary);
    font-weight: 700;
}

#last-event {
    color: var(--warning);
    font-weight: 600;
}

.dashboard h1, .events h1 {
    color: var(--primary);
    font-weight: 700;
    padding-bottom: 0.5rem;
    border-bottom: 3px solid var(--secondary);
    display: inline-block;
    margin-bottom: 1.5rem;
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.1);
}

/* Status indicators */
.status-card {
    transition: all 0.3s ease;
    border-left: 5px solid transparent;
}

.status-on {
    background-color: var(--on-color);
    border-left-color: var(--success);
}

.status-off {
    background-color: var(--off-color);
    border-left-color: var(--danger);
}

/* Dashboard specific */
.dashboard-title {
    color: var(--primary);
    font-weight: 700;
    font-size: 2.5rem;
}

.display-1, .display-2, .display-3, .display-4 {
    font-weight: 700;
    margin: 10px 0;
}

#currentStatus {
    font-size: 3.5rem;
    transition: all 0.3s ease;
}

#todayRuntime, #totalEvents {
    font-size: 2.5rem;
}
/* Add to style.css */
.machine-filter-container {
    margin-right: 15px;
    min-width: 180px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .dashboard-title {
        font-size: 1.8rem;
        margin-bottom: 1rem;
    }
    
    .machine-filter-container {
        margin-right: 0;
        margin-bottom: 10px;
        width: 100%;
    }
}
/* Enhanced Status Card */
.status-card {
    transition: all 0.3s ease;
    border-left: 5px solid transparent;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.status-card.status-on {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border-left-color: var(--success);
    box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
}

.status-card.status-on #currentStatus {
    color: var(--success);
    text-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.status-card.status-off {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border-left-color: var(--danger);
    box-shadow: 0 4px 15px rgba(220, 53, 69, 0.2);
}

.status-card.status-off #currentStatus {
    color: var(--danger);
    text-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* Metric Cards Enhancement */
.metric-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.metric-card:hover::before {
    left: 100%;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.metric-icon {
    opacity: 0.8;
    transition: all 0.3s ease;
}

.metric-card:hover .metric-icon {
    opacity: 1;
    transform: scale(1.1);
}

/* Footer */
footer {
    background-color: var(--light);
    margin-top: auto;
    padding: 1.5rem 0;
    border-top: 1px solid #dee2e6;
}

/* DataTables customization */
.dataTables_wrapper .dataTables_filter input {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 0.375rem 0.75rem;
}

.dataTables_wrapper .dataTables_length select {
    border-radius: 4px;
    border: 1px solid #ced4da;
}

.dataTables_wrapper .dataTables_paginate .paginate_button {
    border-radius: 4px !important;
    margin: 0 2px;
}

.dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: var(--primary) !important;
    border-color: var(--primary) !important;
    color: white !important;
}

/* Enhanced Chart Styling */
.chart-container {
    position: relative;
    height: 350px;
    width: 100%;
    padding: 10px;
}

.chart-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    transition: all 0.3s ease;
    border: 1px solid rgba(0,0,0,0.05);
}

.chart-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.chart-card .card-header {
    border: none;
    padding: 1rem 1.5rem;
    position: relative;
    overflow: hidden;
}

.chart-card .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.chart-card:hover .card-header::before {
    left: 100%;
}

/* Canvas styling */
canvas {
    max-height: 100%;
    border-radius: 8px;
}

/* Equal height cards */
.h-100 {
    height: 100% !important;
}

.row.g-3 > * {
    margin-bottom: 1rem;
}

/* Card body flex for equal heights */
.card-body.d-flex {
    min-height: 200px;
}

/* Chart loading animation */
.chart-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: var(--secondary);
    font-size: 1.1rem;
}

.chart-loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid var(--secondary);
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Chart hover effects */
.card:has(canvas):hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Dashboard metrics cards */
.metric-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.metric-card.success {
    background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.metric-card.info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.metric-card.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

/* Active nav link */
.nav-link.active {
    font-weight: 600;
    position: relative;
}

.nav-link.active::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    right: 0;
    height: 3px;
    background-color: white;
}

/* Enhanced Responsive Design */
@media (max-width: 992px) {
    .chart-container {
        height: 300px;
    }

    .dashboard-title {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }

    .dashboard-title {
        font-size: 1.8rem;
        text-align: center;
        margin-bottom: 1rem;
    }

    .badge.fs-6 {
        font-size: 0.8rem !important;
        margin-top: 0.5rem;
    }

    #currentStatus {
        font-size: 2.5rem;
    }

    #todayRuntime, #totalEvents {
        font-size: 2rem;
    }

    .display-1, .display-2, .display-3, .display-4 {
        font-size: 2rem;
    }

    .chart-container {
        height: 250px;
    }

    .card-body.d-flex {
        min-height: 180px;
    }

    .metric-icon, .status-icon {
        font-size: 2rem !important;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1rem;
    }

    .nav-link {
        padding: 0.5rem 0.5rem;
        font-size: 0.9rem;
    }

    .card {
        border-radius: 8px;
    }

    .dashboard-title {
        font-size: 1.5rem;
    }

    #currentStatus {
        font-size: 2rem;
    }

    #todayRuntime, #totalEvents {
        font-size: 1.5rem;
    }

    .chart-container {
        height: 200px;
        padding: 5px;
    }

    .card-body.d-flex {
        min-height: 150px;
        padding: 1rem !important;
    }

    .card-header {
        padding: 0.75rem 1rem;
    }

    .card-header h5 {
        font-size: 0.9rem;
    }

    .badge {
        font-size: 0.7rem !important;
        padding: 0.25rem 0.5rem !important;
    }
}

/* Loading states */
.card-loading {
    position: relative;
    overflow: hidden;
}

.card-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Smooth transitions for all interactive elements */
* {
    transition: all 0.3s ease;
}

button, .btn, .card, .nav-link {
    transition: all 0.3s ease;
}

/* Table row animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.table-row-fade-in {
    animation: fadeInUp 0.5s ease-out forwards;
    opacity: 0;
}

/* Notification styles */
.alert.position-fixed {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Enhanced button hover effects */
.btn {
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

/* Loading spinner for buttons */
.btn-loading {
    position: relative;
    color: transparent !important;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

/* Enhanced table styling */
.table {
    border-collapse: separate;
    border-spacing: 0;
}

.table thead th {
    position: sticky;
    top: 0;
    background-color: #f8f9fa;
    z-index: 10;
    border-bottom: 2px solid #dee2e6;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* Status indicators with pulse animation */
.status-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-indicator.online {
    background-color: #28a745;
    animation: pulse-green 2s infinite;
}

.status-indicator.offline {
    background-color: #dc3545;
}

@keyframes pulse-green {
    0% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
    }
}

/* Chart loading overlay */
.chart-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.chart-loading-overlay .spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Error states */
.error-state {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.error-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Success animations */
@keyframes checkmark {
    0% {
        transform: scale(0);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.success-checkmark {
    animation: checkmark 0.5s ease-out;
}