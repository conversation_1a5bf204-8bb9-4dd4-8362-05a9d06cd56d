<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - Machine Monitoring System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand d-flex align-items-center" href="/">
                <img src="{{ url_for('static', filename='images/logo.jpeg') }}"
                     alt="Ankur Technologies"
                     class="navbar-logo"
                     style="height: 45px;
                            max-width: 180px;
                            object-fit: contain;
                            margin-right: 15px;
                            background: rgba(255,255,255,0.1);
                            padding: 5px;
                            border-radius: 8px;
                            transition: all 0.3s ease;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);"
                     onerror="this.style.display='none'; this.nextElementSibling.style.display='inline';">
                <span class="fw-bold logo-fallback" style="display: none; color: white; font-size: 1.2rem;">AT</span>
                <span class="fw-bold" style="font-size: 1.1rem; letter-spacing: 0.5px; color: white;">Ankur Technologies</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/dashboard">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/events">
                            <i class="bi bi-list-ul me-1"></i>Events
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="/profile">
                            <i class="bi bi-person-circle me-1"></i>Profile
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="/settings">
                            <i class="bi bi-gear me-1"></i>Settings
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-danger" href="/logout">
                            <i class="bi bi-box-arrow-right me-1"></i>Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container mt-4">
        <div class="row">
            <div class="col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="bi bi-gear me-2"></i>
                            System Settings
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-muted">Display Preferences</h6>
                                <div class="mb-3">
                                    <label class="form-label">Time Zone</label>
                                    <select class="form-select" disabled>
                                        <option selected>IST (Indian Standard Time)</option>
                                    </select>
                                    <small class="text-muted">All timestamps are displayed in IST</small>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Date Format</label>
                                    <select class="form-select" disabled>
                                        <option selected>YYYY-MM-DD HH:MM:SS IST</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Dashboard Refresh Rate</label>
                                    <select class="form-select">
                                        <option value="5">5 seconds</option>
                                        <option value="10" selected>10 seconds</option>
                                        <option value="30">30 seconds</option>
                                        <option value="60">1 minute</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="col-md-6">
                                <h6 class="text-muted">Data Export Settings</h6>
                                <div class="mb-3">
                                    <label class="form-label">Default Export Format</label>
                                    <select class="form-select">
                                        <option value="csv" selected>CSV</option>
                                        <option value="excel">Excel</option>
                                        <option value="json">JSON</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Include Machine ID in Export</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeMachineId" checked>
                                        <label class="form-check-label" for="includeMachineId">
                                            Yes, include machine identification
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label class="form-label">Auto-download Reports</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="autoDownload">
                                        <label class="form-check-label" for="autoDownload">
                                            Automatically download daily reports
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <hr>
                        
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted">Machine Access Information</h6>
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>Your Access Level:</strong> {{ session.role.title() }}<br>
                                    <strong>Accessible Machines:</strong> {{ session.machine_name }}<br>
                                    <strong>Machine IDs:</strong> 
                                    {% for machine_id in session.machine_ids %}
                                        <span class="badge bg-primary me-1">{{ machine_id }}</span>
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-12">
                                <h6 class="text-muted">Actions</h6>
                                <div class="d-flex gap-2 flex-wrap">
                                    <button type="button" class="btn btn-primary" onclick="saveSettings()">
                                        <i class="bi bi-check-circle me-1"></i>
                                        Save Settings
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="resetSettings()">
                                        <i class="bi bi-arrow-clockwise me-1"></i>
                                        Reset to Default
                                    </button>
                                    <a href="/api/export/events-csv" class="btn btn-outline-success">
                                        <i class="bi bi-download me-1"></i>
                                        Export Current Data
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-shield-check me-2"></i>
                            Security & Privacy
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <strong>Data Security:</strong><br>
                                    All data is filtered based on your machine access permissions. You can only view and export data from machines you have been assigned to.
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <strong>Session Security:</strong><br>
                                    Your session is encrypted and will automatically expire for security. Always logout when finished.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function saveSettings() {
            // Placeholder for saving settings
            alert('Settings saved successfully!');
        }
        
        function resetSettings() {
            // Placeholder for resetting settings
            if (confirm('Are you sure you want to reset all settings to default?')) {
                location.reload();
            }
        }
    </script>
</body>
</html>
