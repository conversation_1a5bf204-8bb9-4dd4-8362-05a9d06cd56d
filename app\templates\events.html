{% extends "index.html" %}

{% block content %}
<!-- Events Page Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <h1 class="dashboard-title mb-0">
                <i class="bi bi-list-ul me-2"></i>Machine Events History
            </h1>
            <div class="badge bg-info fs-6 px-3 py-2">
                <i class="bi bi-database me-1"></i>
                <span id="totalEventsCount">Loading...</span> Total Events
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards -->
<div class="row mb-4 g-3">
    <div class="col-lg-4">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);" data-loading="events">
            <div class="card-body text-center text-white p-4">
                <i class="bi bi-clock-history fs-1 mb-3"></i>
                <h5 class="card-title mb-2">Total Runtime Today</h5>
                <h3 class="mb-0" id="todayTotalRuntime">00:00:00</h3>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);" data-loading="events">
            <div class="card-body text-center text-white p-4">
                <i class="bi bi-calendar-event fs-1 mb-3"></i>
                <h5 class="card-title mb-2">Events Today</h5>
                <h3 class="mb-0" id="todayEventsCount">0</h3>
            </div>
        </div>
    </div>
    <div class="col-lg-4">
        <div class="card h-100 border-0 shadow-sm" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);" data-loading="events">
            <div class="card-body text-center text-white p-4">
                <i class="bi bi-stopwatch fs-1 mb-3"></i>
                <h5 class="card-title mb-2">Avg Duration</h5>
                <h3 class="mb-0" id="avgDuration">0 min</h3>
            </div>
        </div>
    </div>
</div>

<!-- Filter and Export Section -->
<div class="row mb-3">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-3">
                <div class="row g-3 align-items-center">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                            <input type="date" class="form-control" id="dateFilter">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">Min Duration</span>
                            <select class="form-select" id="durationFilter">
                                <option value="">All</option>
                                <option value="1">1+ min</option>
                                <option value="5">5+ min</option>
                                <option value="15">15+ min</option>
                                <option value="30">30+ min</option>
                                <option value="60">1+ hour</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <button id="applyFilters" class="btn btn-primary w-100">
                            <i class="bi bi-funnel me-1"></i>Apply Filters
                        </button>
                    </div>
                    <div class="col-md-2">
                        <a href="/api/export/events-csv" class="btn btn-success w-100">
                            <i class="bi bi-download me-1"></i>Export CSV
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Events Table -->
<div class="card border-0 shadow-sm">
    <div class="card-header" style="background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0 text-white">
                <i class="bi bi-table me-2"></i>Machine Events
            </h5>
            <div>
                <button id="refreshBtn" class="btn btn-light btn-sm me-2">
                    <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                </button>
                <div class="btn-group">
                    <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="bi bi-list-ol me-1"></i>Rows
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" data-rows="10">10 rows</a></li>
                        <li><a class="dropdown-item" href="#" data-rows="25">25 rows</a></li>
                        <li><a class="dropdown-item" href="#" data-rows="50">50 rows</a></li>
                        <li><a class="dropdown-item" href="#" data-rows="100">100 rows</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover mb-0" id="eventsTable">
                <thead class="table-light">
                    <tr>
                        <th><i class="bi bi-hash me-1"></i>ID</th>
                        <th><i class="bi bi-play-circle me-1"></i>Start Time</th>
                        <th><i class="bi bi-stop-circle me-1"></i>End Time</th>
                        <th><i class="bi bi-clock me-1"></i>Duration</th>
                        <th><i class="bi bi-activity me-1"></i>State</th>
                    </tr>
                </thead>
                <tbody id="eventsTableBody">
                    <tr>
                        <td colspan="5" class="text-center py-5">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="card-footer bg-light">
        <nav>
            <ul class="pagination justify-content-center mb-0" id="pagination"></ul>
        </nav>
    </div>
</div>

<script>
// Global state
const eventsState = {
    allEvents: [],
    filteredEvents: [],
    currentPage: 1,
    eventsPerPage: 10,
    isLoading: false
};

// Format duration in seconds to HH:MM:SS
function formatDuration(seconds) {
    const totalSeconds = parseInt(seconds) || 0;
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const secs = totalSeconds % 60;
    
    if (hours > 0) {
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    } else if (minutes > 0) {
        return `${minutes}m ${secs.toString().padStart(2, '0')}s`;
    } else {
        return `${secs}s`;
    }
}

// Format date to IST format
function formatDateTime(dateString) {
    try {
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return 'Invalid Date';

        // Convert to IST (Asia/Kolkata timezone)
        const istTime = date.toLocaleString('en-IN', {
            timeZone: 'Asia/Kolkata',
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });

        return istTime + ' IST';
    } catch (error) {
        console.error('Error formatting date:', error);
        // Fallback method if timezone API fails
        try {
            const date = new Date(dateString);
            const istOffset = 5.5 * 60 * 60 * 1000; // 5.5 hours in milliseconds
            const istDate = new Date(date.getTime() + istOffset);

            const year = istDate.getUTCFullYear();
            const month = istDate.getUTCMonth() + 1;
            const day = istDate.getUTCDate();
            const hours = String(istDate.getUTCHours()).padStart(2, '0');
            const minutes = String(istDate.getUTCMinutes()).padStart(2, '0');
            const seconds = String(istDate.getUTCSeconds()).padStart(2, '0');

            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                              'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

            return `${day} ${monthNames[month-1]} ${year}, ${hours}:${minutes}:${seconds} IST`;
        } catch (fallbackError) {
            return 'Invalid Date';
        }
    }
}

// Update summary cards
function updateSummaryCards(events) {
    if (!Array.isArray(events)) return;

    // Get today's date in IST
    const todayIST = new Date().toLocaleDateString('en-CA', {
        timeZone: 'Asia/Kolkata'
    });

    const todayEvents = events.filter(event => {
        const eventDate = new Date(event.start_time).toLocaleDateString('en-CA', {
            timeZone: 'Asia/Kolkata'
        });
        return eventDate === todayIST;
    });
    
    // Update total events count
    document.getElementById('totalEventsCount').textContent = events.length;
    
    // Update today's events count
    document.getElementById('todayEventsCount').textContent = todayEvents.length;
    
    // Calculate today's total runtime
    const todayRuntime = todayEvents.reduce((total, event) => total + (event.duration || 0), 0);
    document.getElementById('todayTotalRuntime').textContent = formatDuration(todayRuntime);
    
    // Calculate average duration
    if (events.length > 0) {
        const avgDuration = events.reduce((total, event) => total + (event.duration || 0), 0) / events.length;
        document.getElementById('avgDuration').textContent = `${Math.round(avgDuration / 60)} min`;
    }
}

// Apply filters to events
function applyFilters() {
    const dateFilter = document.getElementById('dateFilter').value;
    const durationFilter = document.getElementById('durationFilter').value;
    
    eventsState.filteredEvents = [...eventsState.allEvents];
    
    // Apply date filter (using IST dates)
    if (dateFilter) {
        eventsState.filteredEvents = eventsState.filteredEvents.filter(event => {
            const eventDate = new Date(event.start_time).toLocaleDateString('en-CA', {
                timeZone: 'Asia/Kolkata'
            });
            return eventDate === dateFilter;
        });
    }
    
    // Apply duration filter
    if (durationFilter) {
        const minSeconds = parseInt(durationFilter) * 60;
        eventsState.filteredEvents = eventsState.filteredEvents.filter(event => {
            return event.duration >= minSeconds;
        });
    }
    
    // Reset to first page
    eventsState.currentPage = 1;
    
    // Update UI
    renderEventsTable();
    renderPagination();
}

// Render events table
function renderEventsTable() {
    const tbody = document.getElementById('eventsTableBody');
    
    if (!eventsState.filteredEvents || eventsState.filteredEvents.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-5 text-muted">
                    <i class="bi bi-inbox fs-1"></i>
                    <p class="mt-2 mb-0">No events found</p>
                    <p class="small">Try adjusting your filters</p>
                </td>
            </tr>
        `;
        return;
    }
    
    // Calculate pagination
    const startIndex = (eventsState.currentPage - 1) * eventsState.eventsPerPage;
    const endIndex = startIndex + eventsState.eventsPerPage;
    const paginatedEvents = eventsState.filteredEvents.slice(startIndex, endIndex);
    
    // Sort by start_time (newest first)
    paginatedEvents.sort((a, b) => new Date(b.start_time) - new Date(a.start_time));
    
    tbody.innerHTML = '';
    
    paginatedEvents.forEach((event, index) => {
        const row = document.createElement('tr');
        row.className = 'table-row-fade-in';
        row.style.animationDelay = `${index * 0.05}s`;
        
        row.innerHTML = `
            <td><span class="badge bg-secondary">${event.id || 'N/A'}</span></td>
            <td>
                <div class="fw-medium">${formatDateTime(event.start_time)}</div>
            </td>
            <td>
                <div class="fw-medium">${formatDateTime(event.end_time)}</div>
            </td>
            <td>
                <span class="badge bg-info">${formatDuration(event.duration)}</span>
            </td>
            <td>
                <span class="badge bg-success">ON</span>
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Render pagination controls
function renderPagination() {
    const pagination = document.getElementById('pagination');
    if (!pagination) return;
    
    const totalPages = Math.ceil(eventsState.filteredEvents.length / eventsState.eventsPerPage);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // Previous button
    paginationHTML += `
        <li class="page-item ${eventsState.currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="prev">
                <i class="bi bi-chevron-left"></i>
            </a>
        </li>
    `;
    
    // Page numbers
    const maxPagesToShow = 5;
    let startPage = Math.max(1, eventsState.currentPage - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(totalPages, startPage + maxPagesToShow - 1);
    
    if (endPage - startPage + 1 < maxPagesToShow) {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === eventsState.currentPage ? 'active' : ''}">
                <a class="page-link" href="#" data-page="${i}">${i}</a>
            </li>
        `;
    }
    
    // Next button
    paginationHTML += `
        <li class="page-item ${eventsState.currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" data-page="next">
                <i class="bi bi-chevron-right"></i>
            </a>
        </li>
    `;
    
    pagination.innerHTML = paginationHTML;
    
    // Add event listeners
    pagination.querySelectorAll('.page-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const action = this.dataset.page;
            
            if (action === 'prev' && eventsState.currentPage > 1) {
                eventsState.currentPage--;
                renderEventsTable();
            } else if (action === 'next' && eventsState.currentPage < totalPages) {
                eventsState.currentPage++;
                renderEventsTable();
            } else if (!isNaN(action)) {
                eventsState.currentPage = parseInt(action);
                renderEventsTable();
            }
        });
    });
}

// Load events data
async function loadEvents() {
    if (eventsState.isLoading) return;

    eventsState.isLoading = true;
    
    // Show loading state
    document.querySelectorAll('[data-loading="events"]').forEach(el => {
        el.classList.add('card-loading');
    });

    try {
        console.log('🔄 Loading events...');

        // First check health
        const healthResponse = await fetch('/api/health');
        const healthData = await healthResponse.json();
        
        if (!healthResponse.ok || !healthData.supabase_available) {
            throw new Error(`Database connection failed: ${healthData.database_error || 'Database not available'}`);
        }

        // Load events
        const response = await fetch('/api/machine-data');
        const events = await response.json();

        // Check if response has error property
        if (events.error) {
            throw new Error(events.error + (events.details ? ': ' + events.details : ''));
        }

        // Store events in state
        eventsState.allEvents = events;
        eventsState.filteredEvents = [...events];

        // Update UI
        updateSummaryCards(events);
        renderEventsTable();
        renderPagination();

        console.log('✅ Events loaded successfully');

    } catch (error) {
        console.error('❌ Error loading events:', error);

        // Show detailed error message
        let errorMessage = error.message;
        if (error.message.includes('Failed to fetch')) {
            errorMessage = 'Cannot connect to server. Please check if the Flask app is running.';
        }

        document.getElementById('eventsTableBody').innerHTML = `
            <tr>
                <td colspan="5" class="text-center py-5 text-danger">
                    <i class="bi bi-exclamation-triangle fs-1"></i>
                    <p class="mt-2 mb-0"><strong>Failed to load events</strong></p>
                    <p class="small text-muted">${errorMessage}</p>
                    <div class="mt-3">
                        <button class="btn btn-outline-primary btn-sm me-2" onclick="loadEvents()">
                            <i class="bi bi-arrow-clockwise me-1"></i>Try Again
                        </button>
                        <a href="/api/test-connection" target="_blank" class="btn btn-outline-info btn-sm">
                            <i class="bi bi-gear me-1"></i>Test Connection
                        </a>
                    </div>
                </td>
            </tr>
        `;
    } finally {
        eventsState.isLoading = false;
        
        // Hide loading state
        document.querySelectorAll('[data-loading="events"]').forEach(el => {
            el.classList.remove('card-loading');
        });
    }
}

// Initialize events page
document.addEventListener('DOMContentLoaded', function() {
    // Initial load
    loadEvents();
    
    // Set up event listeners
    document.getElementById('applyFilters').addEventListener('click', applyFilters);
    document.getElementById('refreshBtn').addEventListener('click', loadEvents);
    
    // Set up rows per page dropdown
    document.querySelectorAll('.dropdown-item[data-rows]').forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            eventsState.eventsPerPage = parseInt(this.dataset.rows);
            eventsState.currentPage = 1;
            renderEventsTable();
            renderPagination();
        });
    });
    
    // Auto-refresh every 30 seconds
    setInterval(loadEvents, 30000);
});
</script>
{% endblock %}
