#!/usr/bin/env python3
"""
Offline version of the Flask app for testing
This version works without Supabase connection
"""

from flask import Flask, render_template, request, jsonify, session, redirect, url_for, flash
from datetime import datetime, timedelta
import os

print("🚀 Starting Flask App (Offline Mode)")
print("=" * 50)

# Create Flask app
app = Flask(__name__)
app.secret_key = 'test-secret-key-change-in-production'

# Mock data for testing
mock_events = [
    {
        'id': 1,
        'start_time': '2024-01-01T10:00:00',
        'end_time': '2024-01-01T12:00:00',
        'duration': 7200,
        'machine_id': 'machine_1'
    },
    {
        'id': 2,
        'start_time': '2024-01-01T14:00:00',
        'end_time': '2024-01-01T16:30:00',
        'duration': 9000,
        'machine_id': 'machine_2'
    }
]

# Mock users
users = {
    'admin': {'password': 'admin123', 'role': 'admin', 'machine_ids': ['machine_1', 'machine_2', 'machine_3']},
    'pankaj': {'password': 'pankaj123', 'role': 'admin', 'machine_ids': ['machine_1', 'machine_2', 'machine_3']},
    'user1': {'password': 'password1', 'role': 'user', 'machine_ids': ['machine_1']}
}

@app.route('/')
def index():
    if 'username' not in session:
        return redirect(url_for('login'))
    return redirect(url_for('dashboard'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        if username in users and users[username]['password'] == password:
            session['username'] = username
            session['role'] = users[username]['role']
            session['machine_ids'] = users[username]['machine_ids']
            flash(f'Welcome, {username}!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Invalid credentials', 'error')
    
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Login - Machine Monitoring</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 50px; background: #f0f0f0; }
            .login-form { background: white; padding: 30px; border-radius: 10px; max-width: 400px; margin: 0 auto; }
            input { width: 100%; padding: 10px; margin: 10px 0; border: 1px solid #ddd; border-radius: 5px; }
            button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
            .alert { padding: 10px; margin: 10px 0; border-radius: 5px; }
            .alert-error { background: #f8d7da; color: #721c24; }
            .alert-success { background: #d4edda; color: #155724; }
        </style>
    </head>
    <body>
        <div class="login-form">
            <h2>🏭 Machine Monitoring System</h2>
            <p><strong>Offline Test Mode</strong></p>
            <form method="post">
                <input type="text" name="username" placeholder="Username" required>
                <input type="password" name="password" placeholder="Password" required>
                <button type="submit">Login</button>
            </form>
            <hr>
            <p><strong>Test Credentials:</strong></p>
            <p>Admin: <code>admin</code> / <code>admin123</code></p>
            <p>User: <code>pankaj</code> / <code>pankaj123</code></p>
        </div>
    </body>
    </html>
    '''

@app.route('/dashboard')
def dashboard():
    if 'username' not in session:
        return redirect(url_for('login'))
    
    return f'''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Dashboard - Machine Monitoring</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; background: #f8f9fa; }}
            .header {{ background: #007bff; color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
            .card {{ background: white; padding: 20px; border-radius: 10px; margin: 10px 0; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }}
            .status {{ font-size: 24px; font-weight: bold; color: #28a745; }}
            .nav {{ margin: 20px 0; }}
            .nav a {{ margin-right: 20px; color: #007bff; text-decoration: none; }}
            .nav a:hover {{ text-decoration: underline; }}
        </style>
    </head>
    <body>
        <div class="header">
            <h1>🏭 Machine Monitoring Dashboard</h1>
            <p>Welcome, {session['username']}! (Role: {session['role']})</p>
            <p><strong>⚠️ OFFLINE TEST MODE - No Database Connection</strong></p>
        </div>
        
        <div class="nav">
            <a href="/dashboard">Dashboard</a>
            <a href="/events">Events</a>
            <a href="/api/health">Health Check</a>
            <a href="/logout">Logout</a>
        </div>
        
        <div class="card">
            <h3>📊 System Status</h3>
            <div class="status">✅ ONLINE</div>
            <p>Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
        </div>
        
        <div class="card">
            <h3>📈 Quick Stats</h3>
            <p>Total Events: {len(mock_events)}</p>
            <p>Your Machines: {', '.join(session['machine_ids'])}</p>
            <p>Server Time: {datetime.now().isoformat()}</p>
        </div>
        
        <div class="card">
            <h3>🔧 Test Links</h3>
            <p><a href="/api/health">Health Check API</a></p>
            <p><a href="/api/machine-data">Machine Data API</a></p>
            <p><a href="/events">Events Page</a></p>
        </div>
    </body>
    </html>
    '''

@app.route('/events')
def events():
    if 'username' not in session:
        return redirect(url_for('login'))
    
    return f'''
    <!DOCTYPE html>
    <html>
    <head>
        <title>Events - Machine Monitoring</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            table {{ width: 100%; border-collapse: collapse; }}
            th, td {{ border: 1px solid #ddd; padding: 10px; text-align: left; }}
            th {{ background: #f8f9fa; }}
        </style>
    </head>
    <body>
        <h1>📋 Machine Events</h1>
        <p><a href="/dashboard">← Back to Dashboard</a></p>
        
        <table>
            <tr>
                <th>ID</th>
                <th>Start Time</th>
                <th>End Time</th>
                <th>Duration (sec)</th>
                <th>Machine</th>
            </tr>
            {''.join([f'<tr><td>{e["id"]}</td><td>{e["start_time"]}</td><td>{e["end_time"]}</td><td>{e["duration"]}</td><td>{e["machine_id"]}</td></tr>' for e in mock_events])}
        </table>
    </body>
    </html>
    '''

@app.route('/api/health')
def health():
    return jsonify({
        'status': 'healthy',
        'mode': 'offline_test',
        'timestamp': datetime.now().isoformat(),
        'database': 'mock_data',
        'session_active': 'username' in session,
        'user': session.get('username', 'Not logged in')
    })

@app.route('/api/machine-data')
def machine_data():
    if 'username' not in session:
        return jsonify({'error': 'Not logged in'}), 401
    
    return jsonify(mock_events)

@app.route('/logout')
def logout():
    session.clear()
    return redirect(url_for('login'))

if __name__ == '__main__':
    print("🌐 Server will be accessible at:")
    print("   ➤ http://localhost:5000")
    print("   ➤ http://127.0.0.1:5000")
    print("   ➤ http://0.0.0.0:5000")
    print()
    print("🔑 Test Credentials:")
    print("   ➤ admin / admin123")
    print("   ➤ pankaj / pankaj123")
    print()
    print("⏹️  Press Ctrl+C to stop")
    print("=" * 50)
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True)
    except Exception as e:
        print(f"❌ Error starting Flask app: {e}")
        input("Press Enter to exit...")
